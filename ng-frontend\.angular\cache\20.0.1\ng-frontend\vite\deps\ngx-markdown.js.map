{"version": 3, "sources": ["../../../../../../node_modules/.pnpm/@angular+core@20.0.2_@angul_379dce4d468efe01f0e150e802781a86/node_modules/@angular/core/fesm2022/rxjs-interop.mjs", "../../../../../../node_modules/.pnpm/marked@15.0.12/node_modules/marked/lib/marked.esm.js", "../../../../../../node_modules/.pnpm/ngx-markdown@20.0.0_@angula_6b523962a65eecddd02ded86b2ce8683/node_modules/ngx-markdown/fesm2022/ngx-markdown.mjs"], "sourcesContent": ["/**\n * @license Angular v20.0.2\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { Observable, ReplaySubject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport { assertInInjectionContext, inject, DestroyRef, RuntimeError, Injector, assertNotInReactiveContext, signal, PendingTasks } from './root_effect_scheduler-DCy1y1b8.mjs';\nimport { getOutputDestroyRef, effect, untracked, computed, resource, encapsulateResourceError } from './resource-BarKSp_3.mjs';\nimport './primitives/di.mjs';\nimport './signal-nCiHhWf6.mjs';\nimport '@angular/core/primitives/di';\nimport '@angular/core/primitives/signals';\nimport './untracked-DmD_2MlC.mjs';\n\n/**\n * Operator which completes the Observable when the calling context (component, directive, service,\n * etc) is destroyed.\n *\n * @param destroyRef optionally, the `DestroyRef` representing the current context. This can be\n *     passed explicitly to use `takeUntilDestroyed` outside of an [injection\n * context](guide/di/dependency-injection-context). Otherwise, the current `DestroyRef` is injected.\n *\n * @publicApi 19.0\n */\nfunction takeUntilDestroyed(destroyRef) {\n    if (!destroyRef) {\n        ngDevMode && assertInInjectionContext(takeUntilDestroyed);\n        destroyRef = inject(DestroyRef);\n    }\n    const destroyed$ = new Observable((subscriber) => {\n        if (destroyRef.destroyed) {\n            subscriber.next();\n            return;\n        }\n        const unregisterFn = destroyRef.onDestroy(subscriber.next.bind(subscriber));\n        return unregisterFn;\n    });\n    return (source) => {\n        return source.pipe(takeUntil(destroyed$));\n    };\n}\n\n/**\n * Implementation of `OutputRef` that emits values from\n * an RxJS observable source.\n *\n * @internal\n */\nclass OutputFromObservableRef {\n    source;\n    destroyed = false;\n    destroyRef = inject(DestroyRef);\n    constructor(source) {\n        this.source = source;\n        this.destroyRef.onDestroy(() => {\n            this.destroyed = true;\n        });\n    }\n    subscribe(callbackFn) {\n        if (this.destroyed) {\n            throw new RuntimeError(953 /* ɵRuntimeErrorCode.OUTPUT_REF_DESTROYED */, ngDevMode &&\n                'Unexpected subscription to destroyed `OutputRef`. ' +\n                    'The owning directive/component is destroyed.');\n        }\n        // Stop yielding more values when the directive/component is already destroyed.\n        const subscription = this.source.pipe(takeUntilDestroyed(this.destroyRef)).subscribe({\n            next: (value) => callbackFn(value),\n        });\n        return {\n            unsubscribe: () => subscription.unsubscribe(),\n        };\n    }\n}\n/**\n * Declares an Angular output that is using an RxJS observable as a source\n * for events dispatched to parent subscribers.\n *\n * The behavior for an observable as source is defined as followed:\n *    1. New values are forwarded to the Angular output (next notifications).\n *    2. Errors notifications are not handled by Angular. You need to handle these manually.\n *       For example by using `catchError`.\n *    3. Completion notifications stop the output from emitting new values.\n *\n * @usageNotes\n * Initialize an output in your directive by declaring a\n * class field and initializing it with the `outputFromObservable()` function.\n *\n * ```ts\n * @Directive({..})\n * export class MyDir {\n *   nameChange$ = <some-observable>;\n *   nameChange = outputFromObservable(this.nameChange$);\n * }\n * ```\n *\n * @publicApi 19.0\n */\nfunction outputFromObservable(observable, opts) {\n    ngDevMode && assertInInjectionContext(outputFromObservable);\n    return new OutputFromObservableRef(observable);\n}\n\n/**\n * Converts an Angular output declared via `output()` or `outputFromObservable()`\n * to an observable.\n *\n * You can subscribe to the output via `Observable.subscribe` then.\n *\n * @publicApi 19.0\n */\nfunction outputToObservable(ref) {\n    const destroyRef = getOutputDestroyRef(ref);\n    return new Observable((observer) => {\n        // Complete the observable upon directive/component destroy.\n        // Note: May be `undefined` if an `EventEmitter` is declared outside\n        // of an injection context.\n        const unregisterOnDestroy = destroyRef?.onDestroy(() => observer.complete());\n        const subscription = ref.subscribe((v) => observer.next(v));\n        return () => {\n            subscription.unsubscribe();\n            unregisterOnDestroy?.();\n        };\n    });\n}\n\n/**\n * Exposes the value of an Angular `Signal` as an RxJS `Observable`.\n *\n * The signal's value will be propagated into the `Observable`'s subscribers using an `effect`.\n *\n * `toObservable` must be called in an injection context unless an injector is provided via options.\n *\n * @publicApi 20.0\n */\nfunction toObservable(source, options) {\n    if (ngDevMode && !options?.injector) {\n        assertInInjectionContext(toObservable);\n    }\n    const injector = options?.injector ?? inject(Injector);\n    const subject = new ReplaySubject(1);\n    const watcher = effect(() => {\n        let value;\n        try {\n            value = source();\n        }\n        catch (err) {\n            untracked(() => subject.error(err));\n            return;\n        }\n        untracked(() => subject.next(value));\n    }, { injector, manualCleanup: true });\n    injector.get(DestroyRef).onDestroy(() => {\n        watcher.destroy();\n        subject.complete();\n    });\n    return subject.asObservable();\n}\n\n/**\n * Get the current value of an `Observable` as a reactive `Signal`.\n *\n * `toSignal` returns a `Signal` which provides synchronous reactive access to values produced\n * by the given `Observable`, by subscribing to that `Observable`. The returned `Signal` will always\n * have the most recent value emitted by the subscription, and will throw an error if the\n * `Observable` errors.\n *\n * With `requireSync` set to `true`, `toSignal` will assert that the `Observable` produces a value\n * immediately upon subscription. No `initialValue` is needed in this case, and the returned signal\n * does not include an `undefined` type.\n *\n * By default, the subscription will be automatically cleaned up when the current [injection\n * context](guide/di/dependency-injection-context) is destroyed. For example, when `toSignal` is\n * called during the construction of a component, the subscription will be cleaned up when the\n * component is destroyed. If an injection context is not available, an explicit `Injector` can be\n * passed instead.\n *\n * If the subscription should persist until the `Observable` itself completes, the `manualCleanup`\n * option can be specified instead, which disables the automatic subscription teardown. No injection\n * context is needed in this configuration as well.\n */\nfunction toSignal(source, options) {\n    typeof ngDevMode !== 'undefined' &&\n        ngDevMode &&\n        assertNotInReactiveContext(toSignal, 'Invoking `toSignal` causes new subscriptions every time. ' +\n            'Consider moving `toSignal` outside of the reactive context and read the signal value where needed.');\n    const requiresCleanup = !options?.manualCleanup;\n    if (ngDevMode && requiresCleanup && !options?.injector) {\n        assertInInjectionContext(toSignal);\n    }\n    const cleanupRef = requiresCleanup\n        ? (options?.injector?.get(DestroyRef) ?? inject(DestroyRef))\n        : null;\n    const equal = makeToSignalEqual(options?.equal);\n    // Note: T is the Observable value type, and U is the initial value type. They don't have to be\n    // the same - the returned signal gives values of type `T`.\n    let state;\n    if (options?.requireSync) {\n        // Initially the signal is in a `NoValue` state.\n        state = signal({ kind: 0 /* StateKind.NoValue */ }, { equal });\n    }\n    else {\n        // If an initial value was passed, use it. Otherwise, use `undefined` as the initial value.\n        state = signal({ kind: 1 /* StateKind.Value */, value: options?.initialValue }, { equal });\n    }\n    let destroyUnregisterFn;\n    // Note: This code cannot run inside a reactive context (see assertion above). If we'd support\n    // this, we would subscribe to the observable outside of the current reactive context, avoiding\n    // that side-effect signal reads/writes are attribute to the current consumer. The current\n    // consumer only needs to be notified when the `state` signal changes through the observable\n    // subscription. Additional context (related to async pipe):\n    // https://github.com/angular/angular/pull/50522.\n    const sub = source.subscribe({\n        next: (value) => state.set({ kind: 1 /* StateKind.Value */, value }),\n        error: (error) => {\n            state.set({ kind: 2 /* StateKind.Error */, error });\n            destroyUnregisterFn?.();\n        },\n        complete: () => {\n            destroyUnregisterFn?.();\n        },\n        // Completion of the Observable is meaningless to the signal. Signals don't have a concept of\n        // \"complete\".\n    });\n    if (options?.requireSync && state().kind === 0 /* StateKind.NoValue */) {\n        throw new RuntimeError(601 /* ɵRuntimeErrorCode.REQUIRE_SYNC_WITHOUT_SYNC_EMIT */, (typeof ngDevMode === 'undefined' || ngDevMode) &&\n            '`toSignal()` called with `requireSync` but `Observable` did not emit synchronously.');\n    }\n    // Unsubscribe when the current context is destroyed, if requested.\n    destroyUnregisterFn = cleanupRef?.onDestroy(sub.unsubscribe.bind(sub));\n    // The actual returned signal is a `computed` of the `State` signal, which maps the various states\n    // to either values or errors.\n    return computed(() => {\n        const current = state();\n        switch (current.kind) {\n            case 1 /* StateKind.Value */:\n                return current.value;\n            case 2 /* StateKind.Error */:\n                throw current.error;\n            case 0 /* StateKind.NoValue */:\n                // This shouldn't really happen because the error is thrown on creation.\n                throw new RuntimeError(601 /* ɵRuntimeErrorCode.REQUIRE_SYNC_WITHOUT_SYNC_EMIT */, (typeof ngDevMode === 'undefined' || ngDevMode) &&\n                    '`toSignal()` called with `requireSync` but `Observable` did not emit synchronously.');\n        }\n    }, { equal: options?.equal });\n}\nfunction makeToSignalEqual(userEquality = Object.is) {\n    return (a, b) => a.kind === 1 /* StateKind.Value */ && b.kind === 1 /* StateKind.Value */ && userEquality(a.value, b.value);\n}\n\n/**\n * Operator which makes the application unstable until the observable emits, completes, errors, or is unsubscribed.\n *\n * Use this operator in observables whose subscriptions are important for rendering and should be included in SSR serialization.\n *\n * @param injector The `Injector` to use during creation. If this is not provided, the current injection context will be used instead (via `inject`).\n *\n * @developerPreview 20.0\n */\nfunction pendingUntilEvent(injector) {\n    if (injector === undefined) {\n        ngDevMode && assertInInjectionContext(pendingUntilEvent);\n        injector = inject(Injector);\n    }\n    const taskService = injector.get(PendingTasks);\n    return (sourceObservable) => {\n        return new Observable((originalSubscriber) => {\n            // create a new task on subscription\n            const removeTask = taskService.add();\n            let cleanedUp = false;\n            function cleanupTask() {\n                if (cleanedUp) {\n                    return;\n                }\n                removeTask();\n                cleanedUp = true;\n            }\n            const innerSubscription = sourceObservable.subscribe({\n                next: (v) => {\n                    originalSubscriber.next(v);\n                    cleanupTask();\n                },\n                complete: () => {\n                    originalSubscriber.complete();\n                    cleanupTask();\n                },\n                error: (e) => {\n                    originalSubscriber.error(e);\n                    cleanupTask();\n                },\n            });\n            innerSubscription.add(() => {\n                originalSubscriber.unsubscribe();\n                cleanupTask();\n            });\n            return innerSubscription;\n        });\n    };\n}\n\nfunction rxResource(opts) {\n    if (ngDevMode && !opts?.injector) {\n        assertInInjectionContext(rxResource);\n    }\n    return resource({\n        ...opts,\n        loader: undefined,\n        stream: (params) => {\n            let sub;\n            // Track the abort listener so it can be removed if the Observable completes (as a memory\n            // optimization).\n            const onAbort = () => sub.unsubscribe();\n            params.abortSignal.addEventListener('abort', onAbort);\n            // Start off stream as undefined.\n            const stream = signal({ value: undefined });\n            let resolve;\n            const promise = new Promise((r) => (resolve = r));\n            function send(value) {\n                stream.set(value);\n                resolve?.(stream);\n                resolve = undefined;\n            }\n            // TODO(alxhub): remove after g3 updated to rename loader -> stream\n            const streamFn = opts.stream ?? opts.loader;\n            if (streamFn === undefined) {\n                throw new RuntimeError(990 /* ɵRuntimeErrorCode.MUST_PROVIDE_STREAM_OPTION */, ngDevMode && `Must provide \\`stream\\` option.`);\n            }\n            sub = streamFn(params).subscribe({\n                next: (value) => send({ value }),\n                error: (error) => {\n                    send({ error: encapsulateResourceError(error) });\n                    params.abortSignal.removeEventListener('abort', onAbort);\n                },\n                complete: () => {\n                    if (resolve) {\n                        send({\n                            error: new RuntimeError(991 /* ɵRuntimeErrorCode.RESOURCE_COMPLETED_BEFORE_PRODUCING_VALUE */, ngDevMode && 'Resource completed before producing a value'),\n                        });\n                    }\n                    params.abortSignal.removeEventListener('abort', onAbort);\n                },\n            });\n            return promise;\n        },\n    });\n}\n\nexport { outputFromObservable, outputToObservable, pendingUntilEvent, rxResource, takeUntilDestroyed, toObservable, toSignal };\n\n", "/**\n * marked v15.0.12 - a markdown parser\n * Copyright (c) 2011-2025, <PERSON>. (MIT Licensed)\n * https://github.com/markedjs/marked\n */\n\n/**\n * DO NOT EDIT THIS FILE\n * The code in this file is generated from files in ./src/\n */\n\n\n// src/defaults.ts\nfunction _getDefaults() {\n  return {\n    async: false,\n    breaks: false,\n    extensions: null,\n    gfm: true,\n    hooks: null,\n    pedantic: false,\n    renderer: null,\n    silent: false,\n    tokenizer: null,\n    walkTokens: null\n  };\n}\nvar _defaults = _getDefaults();\nfunction changeDefaults(newDefaults) {\n  _defaults = newDefaults;\n}\n\n// src/rules.ts\nvar noopTest = { exec: () => null };\nfunction edit(regex, opt = \"\") {\n  let source = typeof regex === \"string\" ? regex : regex.source;\n  const obj = {\n    replace: (name, val) => {\n      let valSource = typeof val === \"string\" ? val : val.source;\n      valSource = valSource.replace(other.caret, \"$1\");\n      source = source.replace(name, valSource);\n      return obj;\n    },\n    getRegex: () => {\n      return new RegExp(source, opt);\n    }\n  };\n  return obj;\n}\nvar other = {\n  codeRemoveIndent: /^(?: {1,4}| {0,3}\\t)/gm,\n  outputLinkReplace: /\\\\([\\[\\]])/g,\n  indentCodeCompensation: /^(\\s+)(?:```)/,\n  beginningSpace: /^\\s+/,\n  endingHash: /#$/,\n  startingSpaceChar: /^ /,\n  endingSpaceChar: / $/,\n  nonSpaceChar: /[^ ]/,\n  newLineCharGlobal: /\\n/g,\n  tabCharGlobal: /\\t/g,\n  multipleSpaceGlobal: /\\s+/g,\n  blankLine: /^[ \\t]*$/,\n  doubleBlankLine: /\\n[ \\t]*\\n[ \\t]*$/,\n  blockquoteStart: /^ {0,3}>/,\n  blockquoteSetextReplace: /\\n {0,3}((?:=+|-+) *)(?=\\n|$)/g,\n  blockquoteSetextReplace2: /^ {0,3}>[ \\t]?/gm,\n  listReplaceTabs: /^\\t+/,\n  listReplaceNesting: /^ {1,4}(?=( {4})*[^ ])/g,\n  listIsTask: /^\\[[ xX]\\] /,\n  listReplaceTask: /^\\[[ xX]\\] +/,\n  anyLine: /\\n.*\\n/,\n  hrefBrackets: /^<(.*)>$/,\n  tableDelimiter: /[:|]/,\n  tableAlignChars: /^\\||\\| *$/g,\n  tableRowBlankLine: /\\n[ \\t]*$/,\n  tableAlignRight: /^ *-+: *$/,\n  tableAlignCenter: /^ *:-+: *$/,\n  tableAlignLeft: /^ *:-+ *$/,\n  startATag: /^<a /i,\n  endATag: /^<\\/a>/i,\n  startPreScriptTag: /^<(pre|code|kbd|script)(\\s|>)/i,\n  endPreScriptTag: /^<\\/(pre|code|kbd|script)(\\s|>)/i,\n  startAngleBracket: /^</,\n  endAngleBracket: />$/,\n  pedanticHrefTitle: /^([^'\"]*[^\\s])\\s+(['\"])(.*)\\2/,\n  unicodeAlphaNumeric: /[\\p{L}\\p{N}]/u,\n  escapeTest: /[&<>\"']/,\n  escapeReplace: /[&<>\"']/g,\n  escapeTestNoEncode: /[<>\"']|&(?!(#\\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\\w+);)/,\n  escapeReplaceNoEncode: /[<>\"']|&(?!(#\\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\\w+);)/g,\n  unescapeTest: /&(#(?:\\d+)|(?:#x[0-9A-Fa-f]+)|(?:\\w+));?/ig,\n  caret: /(^|[^\\[])\\^/g,\n  percentDecode: /%25/g,\n  findPipe: /\\|/g,\n  splitPipe: / \\|/,\n  slashPipe: /\\\\\\|/g,\n  carriageReturn: /\\r\\n|\\r/g,\n  spaceLine: /^ +$/gm,\n  notSpaceStart: /^\\S*/,\n  endingNewline: /\\n$/,\n  listItemRegex: (bull) => new RegExp(`^( {0,3}${bull})((?:[\t ][^\\\\n]*)?(?:\\\\n|$))`),\n  nextBulletRegex: (indent) => new RegExp(`^ {0,${Math.min(3, indent - 1)}}(?:[*+-]|\\\\d{1,9}[.)])((?:[ \t][^\\\\n]*)?(?:\\\\n|$))`),\n  hrRegex: (indent) => new RegExp(`^ {0,${Math.min(3, indent - 1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\\\* *){3,})(?:\\\\n+|$)`),\n  fencesBeginRegex: (indent) => new RegExp(`^ {0,${Math.min(3, indent - 1)}}(?:\\`\\`\\`|~~~)`),\n  headingBeginRegex: (indent) => new RegExp(`^ {0,${Math.min(3, indent - 1)}}#`),\n  htmlBeginRegex: (indent) => new RegExp(`^ {0,${Math.min(3, indent - 1)}}<(?:[a-z].*>|!--)`, \"i\")\n};\nvar newline = /^(?:[ \\t]*(?:\\n|$))+/;\nvar blockCode = /^((?: {4}| {0,3}\\t)[^\\n]+(?:\\n(?:[ \\t]*(?:\\n|$))*)?)+/;\nvar fences = /^ {0,3}(`{3,}(?=[^`\\n]*(?:\\n|$))|~{3,})([^\\n]*)(?:\\n|$)(?:|([\\s\\S]*?)(?:\\n|$))(?: {0,3}\\1[~`]* *(?=\\n|$)|$)/;\nvar hr = /^ {0,3}((?:-[\\t ]*){3,}|(?:_[ \\t]*){3,}|(?:\\*[ \\t]*){3,})(?:\\n+|$)/;\nvar heading = /^ {0,3}(#{1,6})(?=\\s|$)(.*)(?:\\n+|$)/;\nvar bullet = /(?:[*+-]|\\d{1,9}[.)])/;\nvar lheadingCore = /^(?!bull |blockCode|fences|blockquote|heading|html|table)((?:.|\\n(?!\\s*?\\n|bull |blockCode|fences|blockquote|heading|html|table))+?)\\n {0,3}(=+|-+) *(?:\\n+|$)/;\nvar lheading = edit(lheadingCore).replace(/bull/g, bullet).replace(/blockCode/g, /(?: {4}| {0,3}\\t)/).replace(/fences/g, / {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g, / {0,3}>/).replace(/heading/g, / {0,3}#{1,6}/).replace(/html/g, / {0,3}<[^\\n>]+>\\n/).replace(/\\|table/g, \"\").getRegex();\nvar lheadingGfm = edit(lheadingCore).replace(/bull/g, bullet).replace(/blockCode/g, /(?: {4}| {0,3}\\t)/).replace(/fences/g, / {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g, / {0,3}>/).replace(/heading/g, / {0,3}#{1,6}/).replace(/html/g, / {0,3}<[^\\n>]+>\\n/).replace(/table/g, / {0,3}\\|?(?:[:\\- ]*\\|)+[\\:\\- ]*\\n/).getRegex();\nvar _paragraph = /^([^\\n]+(?:\\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\\n)[^\\n]+)*)/;\nvar blockText = /^[^\\n]+/;\nvar _blockLabel = /(?!\\s*\\])(?:\\\\.|[^\\[\\]\\\\])+/;\nvar def = edit(/^ {0,3}\\[(label)\\]: *(?:\\n[ \\t]*)?([^<\\s][^\\s]*|<.*?>)(?:(?: +(?:\\n[ \\t]*)?| *\\n[ \\t]*)(title))? *(?:\\n+|$)/).replace(\"label\", _blockLabel).replace(\"title\", /(?:\"(?:\\\\\"?|[^\"\\\\])*\"|'[^'\\n]*(?:\\n[^'\\n]+)*\\n?'|\\([^()]*\\))/).getRegex();\nvar list = edit(/^( {0,3}bull)([ \\t][^\\n]+?)?(?:\\n|$)/).replace(/bull/g, bullet).getRegex();\nvar _tag = \"address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|search|section|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul\";\nvar _comment = /<!--(?:-?>|[\\s\\S]*?(?:-->|$))/;\nvar html = edit(\n  \"^ {0,3}(?:<(script|pre|style|textarea)[\\\\s>][\\\\s\\\\S]*?(?:</\\\\1>[^\\\\n]*\\\\n+|$)|comment[^\\\\n]*(\\\\n+|$)|<\\\\?[\\\\s\\\\S]*?(?:\\\\?>\\\\n*|$)|<![A-Z][\\\\s\\\\S]*?(?:>\\\\n*|$)|<!\\\\[CDATA\\\\[[\\\\s\\\\S]*?(?:\\\\]\\\\]>\\\\n*|$)|</?(tag)(?: +|\\\\n|/?>)[\\\\s\\\\S]*?(?:(?:\\\\n[ \t]*)+\\\\n|$)|<(?!script|pre|style|textarea)([a-z][\\\\w-]*)(?:attribute)*? */?>(?=[ \\\\t]*(?:\\\\n|$))[\\\\s\\\\S]*?(?:(?:\\\\n[ \t]*)+\\\\n|$)|</(?!script|pre|style|textarea)[a-z][\\\\w-]*\\\\s*>(?=[ \\\\t]*(?:\\\\n|$))[\\\\s\\\\S]*?(?:(?:\\\\n[ \t]*)+\\\\n|$))\",\n  \"i\"\n).replace(\"comment\", _comment).replace(\"tag\", _tag).replace(\"attribute\", / +[a-zA-Z:_][\\w.:-]*(?: *= *\"[^\"\\n]*\"| *= *'[^'\\n]*'| *= *[^\\s\"'=<>`]+)?/).getRegex();\nvar paragraph = edit(_paragraph).replace(\"hr\", hr).replace(\"heading\", \" {0,3}#{1,6}(?:\\\\s|$)\").replace(\"|lheading\", \"\").replace(\"|table\", \"\").replace(\"blockquote\", \" {0,3}>\").replace(\"fences\", \" {0,3}(?:`{3,}(?=[^`\\\\n]*\\\\n)|~{3,})[^\\\\n]*\\\\n\").replace(\"list\", \" {0,3}(?:[*+-]|1[.)]) \").replace(\"html\", \"</?(?:tag)(?: +|\\\\n|/?>)|<(?:script|pre|style|textarea|!--)\").replace(\"tag\", _tag).getRegex();\nvar blockquote = edit(/^( {0,3}> ?(paragraph|[^\\n]*)(?:\\n|$))+/).replace(\"paragraph\", paragraph).getRegex();\nvar blockNormal = {\n  blockquote,\n  code: blockCode,\n  def,\n  fences,\n  heading,\n  hr,\n  html,\n  lheading,\n  list,\n  newline,\n  paragraph,\n  table: noopTest,\n  text: blockText\n};\nvar gfmTable = edit(\n  \"^ *([^\\\\n ].*)\\\\n {0,3}((?:\\\\| *)?:?-+:? *(?:\\\\| *:?-+:? *)*(?:\\\\| *)?)(?:\\\\n((?:(?! *\\\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\\\n|$))*)\\\\n*|$)\"\n).replace(\"hr\", hr).replace(\"heading\", \" {0,3}#{1,6}(?:\\\\s|$)\").replace(\"blockquote\", \" {0,3}>\").replace(\"code\", \"(?: {4}| {0,3}\t)[^\\\\n]\").replace(\"fences\", \" {0,3}(?:`{3,}(?=[^`\\\\n]*\\\\n)|~{3,})[^\\\\n]*\\\\n\").replace(\"list\", \" {0,3}(?:[*+-]|1[.)]) \").replace(\"html\", \"</?(?:tag)(?: +|\\\\n|/?>)|<(?:script|pre|style|textarea|!--)\").replace(\"tag\", _tag).getRegex();\nvar blockGfm = {\n  ...blockNormal,\n  lheading: lheadingGfm,\n  table: gfmTable,\n  paragraph: edit(_paragraph).replace(\"hr\", hr).replace(\"heading\", \" {0,3}#{1,6}(?:\\\\s|$)\").replace(\"|lheading\", \"\").replace(\"table\", gfmTable).replace(\"blockquote\", \" {0,3}>\").replace(\"fences\", \" {0,3}(?:`{3,}(?=[^`\\\\n]*\\\\n)|~{3,})[^\\\\n]*\\\\n\").replace(\"list\", \" {0,3}(?:[*+-]|1[.)]) \").replace(\"html\", \"</?(?:tag)(?: +|\\\\n|/?>)|<(?:script|pre|style|textarea|!--)\").replace(\"tag\", _tag).getRegex()\n};\nvar blockPedantic = {\n  ...blockNormal,\n  html: edit(\n    `^ *(?:comment *(?:\\\\n|\\\\s*$)|<(tag)[\\\\s\\\\S]+?</\\\\1> *(?:\\\\n{2,}|\\\\s*$)|<tag(?:\"[^\"]*\"|'[^']*'|\\\\s[^'\"/>\\\\s]*)*?/?> *(?:\\\\n{2,}|\\\\s*$))`\n  ).replace(\"comment\", _comment).replace(/tag/g, \"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\\\b)\\\\w+(?!:|[^\\\\w\\\\s@]*@)\\\\b\").getRegex(),\n  def: /^ *\\[([^\\]]+)\\]: *<?([^\\s>]+)>?(?: +([\"(][^\\n]+[\")]))? *(?:\\n+|$)/,\n  heading: /^(#{1,6})(.*)(?:\\n+|$)/,\n  fences: noopTest,\n  // fences not supported\n  lheading: /^(.+?)\\n {0,3}(=+|-+) *(?:\\n+|$)/,\n  paragraph: edit(_paragraph).replace(\"hr\", hr).replace(\"heading\", \" *#{1,6} *[^\\n]\").replace(\"lheading\", lheading).replace(\"|table\", \"\").replace(\"blockquote\", \" {0,3}>\").replace(\"|fences\", \"\").replace(\"|list\", \"\").replace(\"|html\", \"\").replace(\"|tag\", \"\").getRegex()\n};\nvar escape = /^\\\\([!\"#$%&'()*+,\\-./:;<=>?@\\[\\]\\\\^_`{|}~])/;\nvar inlineCode = /^(`+)([^`]|[^`][\\s\\S]*?[^`])\\1(?!`)/;\nvar br = /^( {2,}|\\\\)\\n(?!\\s*$)/;\nvar inlineText = /^(`+|[^`])(?:(?= {2,}\\n)|[\\s\\S]*?(?:(?=[\\\\<!\\[`*_]|\\b_|$)|[^ ](?= {2,}\\n)))/;\nvar _punctuation = /[\\p{P}\\p{S}]/u;\nvar _punctuationOrSpace = /[\\s\\p{P}\\p{S}]/u;\nvar _notPunctuationOrSpace = /[^\\s\\p{P}\\p{S}]/u;\nvar punctuation = edit(/^((?![*_])punctSpace)/, \"u\").replace(/punctSpace/g, _punctuationOrSpace).getRegex();\nvar _punctuationGfmStrongEm = /(?!~)[\\p{P}\\p{S}]/u;\nvar _punctuationOrSpaceGfmStrongEm = /(?!~)[\\s\\p{P}\\p{S}]/u;\nvar _notPunctuationOrSpaceGfmStrongEm = /(?:[^\\s\\p{P}\\p{S}]|~)/u;\nvar blockSkip = /\\[[^[\\]]*?\\]\\((?:\\\\.|[^\\\\\\(\\)]|\\((?:\\\\.|[^\\\\\\(\\)])*\\))*\\)|`[^`]*?`|<[^<>]*?>/g;\nvar emStrongLDelimCore = /^(?:\\*+(?:((?!\\*)punct)|[^\\s*]))|^_+(?:((?!_)punct)|([^\\s_]))/;\nvar emStrongLDelim = edit(emStrongLDelimCore, \"u\").replace(/punct/g, _punctuation).getRegex();\nvar emStrongLDelimGfm = edit(emStrongLDelimCore, \"u\").replace(/punct/g, _punctuationGfmStrongEm).getRegex();\nvar emStrongRDelimAstCore = \"^[^_*]*?__[^_*]*?\\\\*[^_*]*?(?=__)|[^*]+(?=[^*])|(?!\\\\*)punct(\\\\*+)(?=[\\\\s]|$)|notPunctSpace(\\\\*+)(?!\\\\*)(?=punctSpace|$)|(?!\\\\*)punctSpace(\\\\*+)(?=notPunctSpace)|[\\\\s](\\\\*+)(?!\\\\*)(?=punct)|(?!\\\\*)punct(\\\\*+)(?!\\\\*)(?=punct)|notPunctSpace(\\\\*+)(?=notPunctSpace)\";\nvar emStrongRDelimAst = edit(emStrongRDelimAstCore, \"gu\").replace(/notPunctSpace/g, _notPunctuationOrSpace).replace(/punctSpace/g, _punctuationOrSpace).replace(/punct/g, _punctuation).getRegex();\nvar emStrongRDelimAstGfm = edit(emStrongRDelimAstCore, \"gu\").replace(/notPunctSpace/g, _notPunctuationOrSpaceGfmStrongEm).replace(/punctSpace/g, _punctuationOrSpaceGfmStrongEm).replace(/punct/g, _punctuationGfmStrongEm).getRegex();\nvar emStrongRDelimUnd = edit(\n  \"^[^_*]*?\\\\*\\\\*[^_*]*?_[^_*]*?(?=\\\\*\\\\*)|[^_]+(?=[^_])|(?!_)punct(_+)(?=[\\\\s]|$)|notPunctSpace(_+)(?!_)(?=punctSpace|$)|(?!_)punctSpace(_+)(?=notPunctSpace)|[\\\\s](_+)(?!_)(?=punct)|(?!_)punct(_+)(?!_)(?=punct)\",\n  \"gu\"\n).replace(/notPunctSpace/g, _notPunctuationOrSpace).replace(/punctSpace/g, _punctuationOrSpace).replace(/punct/g, _punctuation).getRegex();\nvar anyPunctuation = edit(/\\\\(punct)/, \"gu\").replace(/punct/g, _punctuation).getRegex();\nvar autolink = edit(/^<(scheme:[^\\s\\x00-\\x1f<>]*|email)>/).replace(\"scheme\", /[a-zA-Z][a-zA-Z0-9+.-]{1,31}/).replace(\"email\", /[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/).getRegex();\nvar _inlineComment = edit(_comment).replace(\"(?:-->|$)\", \"-->\").getRegex();\nvar tag = edit(\n  \"^comment|^</[a-zA-Z][\\\\w:-]*\\\\s*>|^<[a-zA-Z][\\\\w-]*(?:attribute)*?\\\\s*/?>|^<\\\\?[\\\\s\\\\S]*?\\\\?>|^<![a-zA-Z]+\\\\s[\\\\s\\\\S]*?>|^<!\\\\[CDATA\\\\[[\\\\s\\\\S]*?\\\\]\\\\]>\"\n).replace(\"comment\", _inlineComment).replace(\"attribute\", /\\s+[a-zA-Z:_][\\w.:-]*(?:\\s*=\\s*\"[^\"]*\"|\\s*=\\s*'[^']*'|\\s*=\\s*[^\\s\"'=<>`]+)?/).getRegex();\nvar _inlineLabel = /(?:\\[(?:\\\\.|[^\\[\\]\\\\])*\\]|\\\\.|`[^`]*`|[^\\[\\]\\\\`])*?/;\nvar link = edit(/^!?\\[(label)\\]\\(\\s*(href)(?:(?:[ \\t]*(?:\\n[ \\t]*)?)(title))?\\s*\\)/).replace(\"label\", _inlineLabel).replace(\"href\", /<(?:\\\\.|[^\\n<>\\\\])+>|[^ \\t\\n\\x00-\\x1f]*/).replace(\"title\", /\"(?:\\\\\"?|[^\"\\\\])*\"|'(?:\\\\'?|[^'\\\\])*'|\\((?:\\\\\\)?|[^)\\\\])*\\)/).getRegex();\nvar reflink = edit(/^!?\\[(label)\\]\\[(ref)\\]/).replace(\"label\", _inlineLabel).replace(\"ref\", _blockLabel).getRegex();\nvar nolink = edit(/^!?\\[(ref)\\](?:\\[\\])?/).replace(\"ref\", _blockLabel).getRegex();\nvar reflinkSearch = edit(\"reflink|nolink(?!\\\\()\", \"g\").replace(\"reflink\", reflink).replace(\"nolink\", nolink).getRegex();\nvar inlineNormal = {\n  _backpedal: noopTest,\n  // only used for GFM url\n  anyPunctuation,\n  autolink,\n  blockSkip,\n  br,\n  code: inlineCode,\n  del: noopTest,\n  emStrongLDelim,\n  emStrongRDelimAst,\n  emStrongRDelimUnd,\n  escape,\n  link,\n  nolink,\n  punctuation,\n  reflink,\n  reflinkSearch,\n  tag,\n  text: inlineText,\n  url: noopTest\n};\nvar inlinePedantic = {\n  ...inlineNormal,\n  link: edit(/^!?\\[(label)\\]\\((.*?)\\)/).replace(\"label\", _inlineLabel).getRegex(),\n  reflink: edit(/^!?\\[(label)\\]\\s*\\[([^\\]]*)\\]/).replace(\"label\", _inlineLabel).getRegex()\n};\nvar inlineGfm = {\n  ...inlineNormal,\n  emStrongRDelimAst: emStrongRDelimAstGfm,\n  emStrongLDelim: emStrongLDelimGfm,\n  url: edit(/^((?:ftp|https?):\\/\\/|www\\.)(?:[a-zA-Z0-9\\-]+\\.?)+[^\\s<]*|^email/, \"i\").replace(\"email\", /[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/).getRegex(),\n  _backpedal: /(?:[^?!.,:;*_'\"~()&]+|\\([^)]*\\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'\"~)]+(?!$))+/,\n  del: /^(~~?)(?=[^\\s~])((?:\\\\.|[^\\\\])*?(?:\\\\.|[^\\s~\\\\]))\\1(?=[^~]|$)/,\n  text: /^([`~]+|[^`~])(?:(?= {2,}\\n)|(?=[a-zA-Z0-9.!#$%&'*+\\/=?_`{\\|}~-]+@)|[\\s\\S]*?(?:(?=[\\\\<!\\[`*~_]|\\b_|https?:\\/\\/|ftp:\\/\\/|www\\.|$)|[^ ](?= {2,}\\n)|[^a-zA-Z0-9.!#$%&'*+\\/=?_`{\\|}~-](?=[a-zA-Z0-9.!#$%&'*+\\/=?_`{\\|}~-]+@)))/\n};\nvar inlineBreaks = {\n  ...inlineGfm,\n  br: edit(br).replace(\"{2,}\", \"*\").getRegex(),\n  text: edit(inlineGfm.text).replace(\"\\\\b_\", \"\\\\b_| {2,}\\\\n\").replace(/\\{2,\\}/g, \"*\").getRegex()\n};\nvar block = {\n  normal: blockNormal,\n  gfm: blockGfm,\n  pedantic: blockPedantic\n};\nvar inline = {\n  normal: inlineNormal,\n  gfm: inlineGfm,\n  breaks: inlineBreaks,\n  pedantic: inlinePedantic\n};\n\n// src/helpers.ts\nvar escapeReplacements = {\n  \"&\": \"&amp;\",\n  \"<\": \"&lt;\",\n  \">\": \"&gt;\",\n  '\"': \"&quot;\",\n  \"'\": \"&#39;\"\n};\nvar getEscapeReplacement = (ch) => escapeReplacements[ch];\nfunction escape2(html2, encode) {\n  if (encode) {\n    if (other.escapeTest.test(html2)) {\n      return html2.replace(other.escapeReplace, getEscapeReplacement);\n    }\n  } else {\n    if (other.escapeTestNoEncode.test(html2)) {\n      return html2.replace(other.escapeReplaceNoEncode, getEscapeReplacement);\n    }\n  }\n  return html2;\n}\nfunction cleanUrl(href) {\n  try {\n    href = encodeURI(href).replace(other.percentDecode, \"%\");\n  } catch {\n    return null;\n  }\n  return href;\n}\nfunction splitCells(tableRow, count) {\n  const row = tableRow.replace(other.findPipe, (match, offset, str) => {\n    let escaped = false;\n    let curr = offset;\n    while (--curr >= 0 && str[curr] === \"\\\\\") escaped = !escaped;\n    if (escaped) {\n      return \"|\";\n    } else {\n      return \" |\";\n    }\n  }), cells = row.split(other.splitPipe);\n  let i = 0;\n  if (!cells[0].trim()) {\n    cells.shift();\n  }\n  if (cells.length > 0 && !cells.at(-1)?.trim()) {\n    cells.pop();\n  }\n  if (count) {\n    if (cells.length > count) {\n      cells.splice(count);\n    } else {\n      while (cells.length < count) cells.push(\"\");\n    }\n  }\n  for (; i < cells.length; i++) {\n    cells[i] = cells[i].trim().replace(other.slashPipe, \"|\");\n  }\n  return cells;\n}\nfunction rtrim(str, c, invert) {\n  const l = str.length;\n  if (l === 0) {\n    return \"\";\n  }\n  let suffLen = 0;\n  while (suffLen < l) {\n    const currChar = str.charAt(l - suffLen - 1);\n    if (currChar === c && !invert) {\n      suffLen++;\n    } else if (currChar !== c && invert) {\n      suffLen++;\n    } else {\n      break;\n    }\n  }\n  return str.slice(0, l - suffLen);\n}\nfunction findClosingBracket(str, b) {\n  if (str.indexOf(b[1]) === -1) {\n    return -1;\n  }\n  let level = 0;\n  for (let i = 0; i < str.length; i++) {\n    if (str[i] === \"\\\\\") {\n      i++;\n    } else if (str[i] === b[0]) {\n      level++;\n    } else if (str[i] === b[1]) {\n      level--;\n      if (level < 0) {\n        return i;\n      }\n    }\n  }\n  if (level > 0) {\n    return -2;\n  }\n  return -1;\n}\n\n// src/Tokenizer.ts\nfunction outputLink(cap, link2, raw, lexer2, rules) {\n  const href = link2.href;\n  const title = link2.title || null;\n  const text = cap[1].replace(rules.other.outputLinkReplace, \"$1\");\n  lexer2.state.inLink = true;\n  const token = {\n    type: cap[0].charAt(0) === \"!\" ? \"image\" : \"link\",\n    raw,\n    href,\n    title,\n    text,\n    tokens: lexer2.inlineTokens(text)\n  };\n  lexer2.state.inLink = false;\n  return token;\n}\nfunction indentCodeCompensation(raw, text, rules) {\n  const matchIndentToCode = raw.match(rules.other.indentCodeCompensation);\n  if (matchIndentToCode === null) {\n    return text;\n  }\n  const indentToCode = matchIndentToCode[1];\n  return text.split(\"\\n\").map((node) => {\n    const matchIndentInNode = node.match(rules.other.beginningSpace);\n    if (matchIndentInNode === null) {\n      return node;\n    }\n    const [indentInNode] = matchIndentInNode;\n    if (indentInNode.length >= indentToCode.length) {\n      return node.slice(indentToCode.length);\n    }\n    return node;\n  }).join(\"\\n\");\n}\nvar _Tokenizer = class {\n  options;\n  rules;\n  // set by the lexer\n  lexer;\n  // set by the lexer\n  constructor(options2) {\n    this.options = options2 || _defaults;\n  }\n  space(src) {\n    const cap = this.rules.block.newline.exec(src);\n    if (cap && cap[0].length > 0) {\n      return {\n        type: \"space\",\n        raw: cap[0]\n      };\n    }\n  }\n  code(src) {\n    const cap = this.rules.block.code.exec(src);\n    if (cap) {\n      const text = cap[0].replace(this.rules.other.codeRemoveIndent, \"\");\n      return {\n        type: \"code\",\n        raw: cap[0],\n        codeBlockStyle: \"indented\",\n        text: !this.options.pedantic ? rtrim(text, \"\\n\") : text\n      };\n    }\n  }\n  fences(src) {\n    const cap = this.rules.block.fences.exec(src);\n    if (cap) {\n      const raw = cap[0];\n      const text = indentCodeCompensation(raw, cap[3] || \"\", this.rules);\n      return {\n        type: \"code\",\n        raw,\n        lang: cap[2] ? cap[2].trim().replace(this.rules.inline.anyPunctuation, \"$1\") : cap[2],\n        text\n      };\n    }\n  }\n  heading(src) {\n    const cap = this.rules.block.heading.exec(src);\n    if (cap) {\n      let text = cap[2].trim();\n      if (this.rules.other.endingHash.test(text)) {\n        const trimmed = rtrim(text, \"#\");\n        if (this.options.pedantic) {\n          text = trimmed.trim();\n        } else if (!trimmed || this.rules.other.endingSpaceChar.test(trimmed)) {\n          text = trimmed.trim();\n        }\n      }\n      return {\n        type: \"heading\",\n        raw: cap[0],\n        depth: cap[1].length,\n        text,\n        tokens: this.lexer.inline(text)\n      };\n    }\n  }\n  hr(src) {\n    const cap = this.rules.block.hr.exec(src);\n    if (cap) {\n      return {\n        type: \"hr\",\n        raw: rtrim(cap[0], \"\\n\")\n      };\n    }\n  }\n  blockquote(src) {\n    const cap = this.rules.block.blockquote.exec(src);\n    if (cap) {\n      let lines = rtrim(cap[0], \"\\n\").split(\"\\n\");\n      let raw = \"\";\n      let text = \"\";\n      const tokens = [];\n      while (lines.length > 0) {\n        let inBlockquote = false;\n        const currentLines = [];\n        let i;\n        for (i = 0; i < lines.length; i++) {\n          if (this.rules.other.blockquoteStart.test(lines[i])) {\n            currentLines.push(lines[i]);\n            inBlockquote = true;\n          } else if (!inBlockquote) {\n            currentLines.push(lines[i]);\n          } else {\n            break;\n          }\n        }\n        lines = lines.slice(i);\n        const currentRaw = currentLines.join(\"\\n\");\n        const currentText = currentRaw.replace(this.rules.other.blockquoteSetextReplace, \"\\n    $1\").replace(this.rules.other.blockquoteSetextReplace2, \"\");\n        raw = raw ? `${raw}\n${currentRaw}` : currentRaw;\n        text = text ? `${text}\n${currentText}` : currentText;\n        const top = this.lexer.state.top;\n        this.lexer.state.top = true;\n        this.lexer.blockTokens(currentText, tokens, true);\n        this.lexer.state.top = top;\n        if (lines.length === 0) {\n          break;\n        }\n        const lastToken = tokens.at(-1);\n        if (lastToken?.type === \"code\") {\n          break;\n        } else if (lastToken?.type === \"blockquote\") {\n          const oldToken = lastToken;\n          const newText = oldToken.raw + \"\\n\" + lines.join(\"\\n\");\n          const newToken = this.blockquote(newText);\n          tokens[tokens.length - 1] = newToken;\n          raw = raw.substring(0, raw.length - oldToken.raw.length) + newToken.raw;\n          text = text.substring(0, text.length - oldToken.text.length) + newToken.text;\n          break;\n        } else if (lastToken?.type === \"list\") {\n          const oldToken = lastToken;\n          const newText = oldToken.raw + \"\\n\" + lines.join(\"\\n\");\n          const newToken = this.list(newText);\n          tokens[tokens.length - 1] = newToken;\n          raw = raw.substring(0, raw.length - lastToken.raw.length) + newToken.raw;\n          text = text.substring(0, text.length - oldToken.raw.length) + newToken.raw;\n          lines = newText.substring(tokens.at(-1).raw.length).split(\"\\n\");\n          continue;\n        }\n      }\n      return {\n        type: \"blockquote\",\n        raw,\n        tokens,\n        text\n      };\n    }\n  }\n  list(src) {\n    let cap = this.rules.block.list.exec(src);\n    if (cap) {\n      let bull = cap[1].trim();\n      const isordered = bull.length > 1;\n      const list2 = {\n        type: \"list\",\n        raw: \"\",\n        ordered: isordered,\n        start: isordered ? +bull.slice(0, -1) : \"\",\n        loose: false,\n        items: []\n      };\n      bull = isordered ? `\\\\d{1,9}\\\\${bull.slice(-1)}` : `\\\\${bull}`;\n      if (this.options.pedantic) {\n        bull = isordered ? bull : \"[*+-]\";\n      }\n      const itemRegex = this.rules.other.listItemRegex(bull);\n      let endsWithBlankLine = false;\n      while (src) {\n        let endEarly = false;\n        let raw = \"\";\n        let itemContents = \"\";\n        if (!(cap = itemRegex.exec(src))) {\n          break;\n        }\n        if (this.rules.block.hr.test(src)) {\n          break;\n        }\n        raw = cap[0];\n        src = src.substring(raw.length);\n        let line = cap[2].split(\"\\n\", 1)[0].replace(this.rules.other.listReplaceTabs, (t) => \" \".repeat(3 * t.length));\n        let nextLine = src.split(\"\\n\", 1)[0];\n        let blankLine = !line.trim();\n        let indent = 0;\n        if (this.options.pedantic) {\n          indent = 2;\n          itemContents = line.trimStart();\n        } else if (blankLine) {\n          indent = cap[1].length + 1;\n        } else {\n          indent = cap[2].search(this.rules.other.nonSpaceChar);\n          indent = indent > 4 ? 1 : indent;\n          itemContents = line.slice(indent);\n          indent += cap[1].length;\n        }\n        if (blankLine && this.rules.other.blankLine.test(nextLine)) {\n          raw += nextLine + \"\\n\";\n          src = src.substring(nextLine.length + 1);\n          endEarly = true;\n        }\n        if (!endEarly) {\n          const nextBulletRegex = this.rules.other.nextBulletRegex(indent);\n          const hrRegex = this.rules.other.hrRegex(indent);\n          const fencesBeginRegex = this.rules.other.fencesBeginRegex(indent);\n          const headingBeginRegex = this.rules.other.headingBeginRegex(indent);\n          const htmlBeginRegex = this.rules.other.htmlBeginRegex(indent);\n          while (src) {\n            const rawLine = src.split(\"\\n\", 1)[0];\n            let nextLineWithoutTabs;\n            nextLine = rawLine;\n            if (this.options.pedantic) {\n              nextLine = nextLine.replace(this.rules.other.listReplaceNesting, \"  \");\n              nextLineWithoutTabs = nextLine;\n            } else {\n              nextLineWithoutTabs = nextLine.replace(this.rules.other.tabCharGlobal, \"    \");\n            }\n            if (fencesBeginRegex.test(nextLine)) {\n              break;\n            }\n            if (headingBeginRegex.test(nextLine)) {\n              break;\n            }\n            if (htmlBeginRegex.test(nextLine)) {\n              break;\n            }\n            if (nextBulletRegex.test(nextLine)) {\n              break;\n            }\n            if (hrRegex.test(nextLine)) {\n              break;\n            }\n            if (nextLineWithoutTabs.search(this.rules.other.nonSpaceChar) >= indent || !nextLine.trim()) {\n              itemContents += \"\\n\" + nextLineWithoutTabs.slice(indent);\n            } else {\n              if (blankLine) {\n                break;\n              }\n              if (line.replace(this.rules.other.tabCharGlobal, \"    \").search(this.rules.other.nonSpaceChar) >= 4) {\n                break;\n              }\n              if (fencesBeginRegex.test(line)) {\n                break;\n              }\n              if (headingBeginRegex.test(line)) {\n                break;\n              }\n              if (hrRegex.test(line)) {\n                break;\n              }\n              itemContents += \"\\n\" + nextLine;\n            }\n            if (!blankLine && !nextLine.trim()) {\n              blankLine = true;\n            }\n            raw += rawLine + \"\\n\";\n            src = src.substring(rawLine.length + 1);\n            line = nextLineWithoutTabs.slice(indent);\n          }\n        }\n        if (!list2.loose) {\n          if (endsWithBlankLine) {\n            list2.loose = true;\n          } else if (this.rules.other.doubleBlankLine.test(raw)) {\n            endsWithBlankLine = true;\n          }\n        }\n        let istask = null;\n        let ischecked;\n        if (this.options.gfm) {\n          istask = this.rules.other.listIsTask.exec(itemContents);\n          if (istask) {\n            ischecked = istask[0] !== \"[ ] \";\n            itemContents = itemContents.replace(this.rules.other.listReplaceTask, \"\");\n          }\n        }\n        list2.items.push({\n          type: \"list_item\",\n          raw,\n          task: !!istask,\n          checked: ischecked,\n          loose: false,\n          text: itemContents,\n          tokens: []\n        });\n        list2.raw += raw;\n      }\n      const lastItem = list2.items.at(-1);\n      if (lastItem) {\n        lastItem.raw = lastItem.raw.trimEnd();\n        lastItem.text = lastItem.text.trimEnd();\n      } else {\n        return;\n      }\n      list2.raw = list2.raw.trimEnd();\n      for (let i = 0; i < list2.items.length; i++) {\n        this.lexer.state.top = false;\n        list2.items[i].tokens = this.lexer.blockTokens(list2.items[i].text, []);\n        if (!list2.loose) {\n          const spacers = list2.items[i].tokens.filter((t) => t.type === \"space\");\n          const hasMultipleLineBreaks = spacers.length > 0 && spacers.some((t) => this.rules.other.anyLine.test(t.raw));\n          list2.loose = hasMultipleLineBreaks;\n        }\n      }\n      if (list2.loose) {\n        for (let i = 0; i < list2.items.length; i++) {\n          list2.items[i].loose = true;\n        }\n      }\n      return list2;\n    }\n  }\n  html(src) {\n    const cap = this.rules.block.html.exec(src);\n    if (cap) {\n      const token = {\n        type: \"html\",\n        block: true,\n        raw: cap[0],\n        pre: cap[1] === \"pre\" || cap[1] === \"script\" || cap[1] === \"style\",\n        text: cap[0]\n      };\n      return token;\n    }\n  }\n  def(src) {\n    const cap = this.rules.block.def.exec(src);\n    if (cap) {\n      const tag2 = cap[1].toLowerCase().replace(this.rules.other.multipleSpaceGlobal, \" \");\n      const href = cap[2] ? cap[2].replace(this.rules.other.hrefBrackets, \"$1\").replace(this.rules.inline.anyPunctuation, \"$1\") : \"\";\n      const title = cap[3] ? cap[3].substring(1, cap[3].length - 1).replace(this.rules.inline.anyPunctuation, \"$1\") : cap[3];\n      return {\n        type: \"def\",\n        tag: tag2,\n        raw: cap[0],\n        href,\n        title\n      };\n    }\n  }\n  table(src) {\n    const cap = this.rules.block.table.exec(src);\n    if (!cap) {\n      return;\n    }\n    if (!this.rules.other.tableDelimiter.test(cap[2])) {\n      return;\n    }\n    const headers = splitCells(cap[1]);\n    const aligns = cap[2].replace(this.rules.other.tableAlignChars, \"\").split(\"|\");\n    const rows = cap[3]?.trim() ? cap[3].replace(this.rules.other.tableRowBlankLine, \"\").split(\"\\n\") : [];\n    const item = {\n      type: \"table\",\n      raw: cap[0],\n      header: [],\n      align: [],\n      rows: []\n    };\n    if (headers.length !== aligns.length) {\n      return;\n    }\n    for (const align of aligns) {\n      if (this.rules.other.tableAlignRight.test(align)) {\n        item.align.push(\"right\");\n      } else if (this.rules.other.tableAlignCenter.test(align)) {\n        item.align.push(\"center\");\n      } else if (this.rules.other.tableAlignLeft.test(align)) {\n        item.align.push(\"left\");\n      } else {\n        item.align.push(null);\n      }\n    }\n    for (let i = 0; i < headers.length; i++) {\n      item.header.push({\n        text: headers[i],\n        tokens: this.lexer.inline(headers[i]),\n        header: true,\n        align: item.align[i]\n      });\n    }\n    for (const row of rows) {\n      item.rows.push(splitCells(row, item.header.length).map((cell, i) => {\n        return {\n          text: cell,\n          tokens: this.lexer.inline(cell),\n          header: false,\n          align: item.align[i]\n        };\n      }));\n    }\n    return item;\n  }\n  lheading(src) {\n    const cap = this.rules.block.lheading.exec(src);\n    if (cap) {\n      return {\n        type: \"heading\",\n        raw: cap[0],\n        depth: cap[2].charAt(0) === \"=\" ? 1 : 2,\n        text: cap[1],\n        tokens: this.lexer.inline(cap[1])\n      };\n    }\n  }\n  paragraph(src) {\n    const cap = this.rules.block.paragraph.exec(src);\n    if (cap) {\n      const text = cap[1].charAt(cap[1].length - 1) === \"\\n\" ? cap[1].slice(0, -1) : cap[1];\n      return {\n        type: \"paragraph\",\n        raw: cap[0],\n        text,\n        tokens: this.lexer.inline(text)\n      };\n    }\n  }\n  text(src) {\n    const cap = this.rules.block.text.exec(src);\n    if (cap) {\n      return {\n        type: \"text\",\n        raw: cap[0],\n        text: cap[0],\n        tokens: this.lexer.inline(cap[0])\n      };\n    }\n  }\n  escape(src) {\n    const cap = this.rules.inline.escape.exec(src);\n    if (cap) {\n      return {\n        type: \"escape\",\n        raw: cap[0],\n        text: cap[1]\n      };\n    }\n  }\n  tag(src) {\n    const cap = this.rules.inline.tag.exec(src);\n    if (cap) {\n      if (!this.lexer.state.inLink && this.rules.other.startATag.test(cap[0])) {\n        this.lexer.state.inLink = true;\n      } else if (this.lexer.state.inLink && this.rules.other.endATag.test(cap[0])) {\n        this.lexer.state.inLink = false;\n      }\n      if (!this.lexer.state.inRawBlock && this.rules.other.startPreScriptTag.test(cap[0])) {\n        this.lexer.state.inRawBlock = true;\n      } else if (this.lexer.state.inRawBlock && this.rules.other.endPreScriptTag.test(cap[0])) {\n        this.lexer.state.inRawBlock = false;\n      }\n      return {\n        type: \"html\",\n        raw: cap[0],\n        inLink: this.lexer.state.inLink,\n        inRawBlock: this.lexer.state.inRawBlock,\n        block: false,\n        text: cap[0]\n      };\n    }\n  }\n  link(src) {\n    const cap = this.rules.inline.link.exec(src);\n    if (cap) {\n      const trimmedUrl = cap[2].trim();\n      if (!this.options.pedantic && this.rules.other.startAngleBracket.test(trimmedUrl)) {\n        if (!this.rules.other.endAngleBracket.test(trimmedUrl)) {\n          return;\n        }\n        const rtrimSlash = rtrim(trimmedUrl.slice(0, -1), \"\\\\\");\n        if ((trimmedUrl.length - rtrimSlash.length) % 2 === 0) {\n          return;\n        }\n      } else {\n        const lastParenIndex = findClosingBracket(cap[2], \"()\");\n        if (lastParenIndex === -2) {\n          return;\n        }\n        if (lastParenIndex > -1) {\n          const start = cap[0].indexOf(\"!\") === 0 ? 5 : 4;\n          const linkLen = start + cap[1].length + lastParenIndex;\n          cap[2] = cap[2].substring(0, lastParenIndex);\n          cap[0] = cap[0].substring(0, linkLen).trim();\n          cap[3] = \"\";\n        }\n      }\n      let href = cap[2];\n      let title = \"\";\n      if (this.options.pedantic) {\n        const link2 = this.rules.other.pedanticHrefTitle.exec(href);\n        if (link2) {\n          href = link2[1];\n          title = link2[3];\n        }\n      } else {\n        title = cap[3] ? cap[3].slice(1, -1) : \"\";\n      }\n      href = href.trim();\n      if (this.rules.other.startAngleBracket.test(href)) {\n        if (this.options.pedantic && !this.rules.other.endAngleBracket.test(trimmedUrl)) {\n          href = href.slice(1);\n        } else {\n          href = href.slice(1, -1);\n        }\n      }\n      return outputLink(cap, {\n        href: href ? href.replace(this.rules.inline.anyPunctuation, \"$1\") : href,\n        title: title ? title.replace(this.rules.inline.anyPunctuation, \"$1\") : title\n      }, cap[0], this.lexer, this.rules);\n    }\n  }\n  reflink(src, links) {\n    let cap;\n    if ((cap = this.rules.inline.reflink.exec(src)) || (cap = this.rules.inline.nolink.exec(src))) {\n      const linkString = (cap[2] || cap[1]).replace(this.rules.other.multipleSpaceGlobal, \" \");\n      const link2 = links[linkString.toLowerCase()];\n      if (!link2) {\n        const text = cap[0].charAt(0);\n        return {\n          type: \"text\",\n          raw: text,\n          text\n        };\n      }\n      return outputLink(cap, link2, cap[0], this.lexer, this.rules);\n    }\n  }\n  emStrong(src, maskedSrc, prevChar = \"\") {\n    let match = this.rules.inline.emStrongLDelim.exec(src);\n    if (!match) return;\n    if (match[3] && prevChar.match(this.rules.other.unicodeAlphaNumeric)) return;\n    const nextChar = match[1] || match[2] || \"\";\n    if (!nextChar || !prevChar || this.rules.inline.punctuation.exec(prevChar)) {\n      const lLength = [...match[0]].length - 1;\n      let rDelim, rLength, delimTotal = lLength, midDelimTotal = 0;\n      const endReg = match[0][0] === \"*\" ? this.rules.inline.emStrongRDelimAst : this.rules.inline.emStrongRDelimUnd;\n      endReg.lastIndex = 0;\n      maskedSrc = maskedSrc.slice(-1 * src.length + lLength);\n      while ((match = endReg.exec(maskedSrc)) != null) {\n        rDelim = match[1] || match[2] || match[3] || match[4] || match[5] || match[6];\n        if (!rDelim) continue;\n        rLength = [...rDelim].length;\n        if (match[3] || match[4]) {\n          delimTotal += rLength;\n          continue;\n        } else if (match[5] || match[6]) {\n          if (lLength % 3 && !((lLength + rLength) % 3)) {\n            midDelimTotal += rLength;\n            continue;\n          }\n        }\n        delimTotal -= rLength;\n        if (delimTotal > 0) continue;\n        rLength = Math.min(rLength, rLength + delimTotal + midDelimTotal);\n        const lastCharLength = [...match[0]][0].length;\n        const raw = src.slice(0, lLength + match.index + lastCharLength + rLength);\n        if (Math.min(lLength, rLength) % 2) {\n          const text2 = raw.slice(1, -1);\n          return {\n            type: \"em\",\n            raw,\n            text: text2,\n            tokens: this.lexer.inlineTokens(text2)\n          };\n        }\n        const text = raw.slice(2, -2);\n        return {\n          type: \"strong\",\n          raw,\n          text,\n          tokens: this.lexer.inlineTokens(text)\n        };\n      }\n    }\n  }\n  codespan(src) {\n    const cap = this.rules.inline.code.exec(src);\n    if (cap) {\n      let text = cap[2].replace(this.rules.other.newLineCharGlobal, \" \");\n      const hasNonSpaceChars = this.rules.other.nonSpaceChar.test(text);\n      const hasSpaceCharsOnBothEnds = this.rules.other.startingSpaceChar.test(text) && this.rules.other.endingSpaceChar.test(text);\n      if (hasNonSpaceChars && hasSpaceCharsOnBothEnds) {\n        text = text.substring(1, text.length - 1);\n      }\n      return {\n        type: \"codespan\",\n        raw: cap[0],\n        text\n      };\n    }\n  }\n  br(src) {\n    const cap = this.rules.inline.br.exec(src);\n    if (cap) {\n      return {\n        type: \"br\",\n        raw: cap[0]\n      };\n    }\n  }\n  del(src) {\n    const cap = this.rules.inline.del.exec(src);\n    if (cap) {\n      return {\n        type: \"del\",\n        raw: cap[0],\n        text: cap[2],\n        tokens: this.lexer.inlineTokens(cap[2])\n      };\n    }\n  }\n  autolink(src) {\n    const cap = this.rules.inline.autolink.exec(src);\n    if (cap) {\n      let text, href;\n      if (cap[2] === \"@\") {\n        text = cap[1];\n        href = \"mailto:\" + text;\n      } else {\n        text = cap[1];\n        href = text;\n      }\n      return {\n        type: \"link\",\n        raw: cap[0],\n        text,\n        href,\n        tokens: [\n          {\n            type: \"text\",\n            raw: text,\n            text\n          }\n        ]\n      };\n    }\n  }\n  url(src) {\n    let cap;\n    if (cap = this.rules.inline.url.exec(src)) {\n      let text, href;\n      if (cap[2] === \"@\") {\n        text = cap[0];\n        href = \"mailto:\" + text;\n      } else {\n        let prevCapZero;\n        do {\n          prevCapZero = cap[0];\n          cap[0] = this.rules.inline._backpedal.exec(cap[0])?.[0] ?? \"\";\n        } while (prevCapZero !== cap[0]);\n        text = cap[0];\n        if (cap[1] === \"www.\") {\n          href = \"http://\" + cap[0];\n        } else {\n          href = cap[0];\n        }\n      }\n      return {\n        type: \"link\",\n        raw: cap[0],\n        text,\n        href,\n        tokens: [\n          {\n            type: \"text\",\n            raw: text,\n            text\n          }\n        ]\n      };\n    }\n  }\n  inlineText(src) {\n    const cap = this.rules.inline.text.exec(src);\n    if (cap) {\n      const escaped = this.lexer.state.inRawBlock;\n      return {\n        type: \"text\",\n        raw: cap[0],\n        text: cap[0],\n        escaped\n      };\n    }\n  }\n};\n\n// src/Lexer.ts\nvar _Lexer = class __Lexer {\n  tokens;\n  options;\n  state;\n  tokenizer;\n  inlineQueue;\n  constructor(options2) {\n    this.tokens = [];\n    this.tokens.links = /* @__PURE__ */ Object.create(null);\n    this.options = options2 || _defaults;\n    this.options.tokenizer = this.options.tokenizer || new _Tokenizer();\n    this.tokenizer = this.options.tokenizer;\n    this.tokenizer.options = this.options;\n    this.tokenizer.lexer = this;\n    this.inlineQueue = [];\n    this.state = {\n      inLink: false,\n      inRawBlock: false,\n      top: true\n    };\n    const rules = {\n      other,\n      block: block.normal,\n      inline: inline.normal\n    };\n    if (this.options.pedantic) {\n      rules.block = block.pedantic;\n      rules.inline = inline.pedantic;\n    } else if (this.options.gfm) {\n      rules.block = block.gfm;\n      if (this.options.breaks) {\n        rules.inline = inline.breaks;\n      } else {\n        rules.inline = inline.gfm;\n      }\n    }\n    this.tokenizer.rules = rules;\n  }\n  /**\n   * Expose Rules\n   */\n  static get rules() {\n    return {\n      block,\n      inline\n    };\n  }\n  /**\n   * Static Lex Method\n   */\n  static lex(src, options2) {\n    const lexer2 = new __Lexer(options2);\n    return lexer2.lex(src);\n  }\n  /**\n   * Static Lex Inline Method\n   */\n  static lexInline(src, options2) {\n    const lexer2 = new __Lexer(options2);\n    return lexer2.inlineTokens(src);\n  }\n  /**\n   * Preprocessing\n   */\n  lex(src) {\n    src = src.replace(other.carriageReturn, \"\\n\");\n    this.blockTokens(src, this.tokens);\n    for (let i = 0; i < this.inlineQueue.length; i++) {\n      const next = this.inlineQueue[i];\n      this.inlineTokens(next.src, next.tokens);\n    }\n    this.inlineQueue = [];\n    return this.tokens;\n  }\n  blockTokens(src, tokens = [], lastParagraphClipped = false) {\n    if (this.options.pedantic) {\n      src = src.replace(other.tabCharGlobal, \"    \").replace(other.spaceLine, \"\");\n    }\n    while (src) {\n      let token;\n      if (this.options.extensions?.block?.some((extTokenizer) => {\n        if (token = extTokenizer.call({ lexer: this }, src, tokens)) {\n          src = src.substring(token.raw.length);\n          tokens.push(token);\n          return true;\n        }\n        return false;\n      })) {\n        continue;\n      }\n      if (token = this.tokenizer.space(src)) {\n        src = src.substring(token.raw.length);\n        const lastToken = tokens.at(-1);\n        if (token.raw.length === 1 && lastToken !== void 0) {\n          lastToken.raw += \"\\n\";\n        } else {\n          tokens.push(token);\n        }\n        continue;\n      }\n      if (token = this.tokenizer.code(src)) {\n        src = src.substring(token.raw.length);\n        const lastToken = tokens.at(-1);\n        if (lastToken?.type === \"paragraph\" || lastToken?.type === \"text\") {\n          lastToken.raw += \"\\n\" + token.raw;\n          lastToken.text += \"\\n\" + token.text;\n          this.inlineQueue.at(-1).src = lastToken.text;\n        } else {\n          tokens.push(token);\n        }\n        continue;\n      }\n      if (token = this.tokenizer.fences(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      if (token = this.tokenizer.heading(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      if (token = this.tokenizer.hr(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      if (token = this.tokenizer.blockquote(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      if (token = this.tokenizer.list(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      if (token = this.tokenizer.html(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      if (token = this.tokenizer.def(src)) {\n        src = src.substring(token.raw.length);\n        const lastToken = tokens.at(-1);\n        if (lastToken?.type === \"paragraph\" || lastToken?.type === \"text\") {\n          lastToken.raw += \"\\n\" + token.raw;\n          lastToken.text += \"\\n\" + token.raw;\n          this.inlineQueue.at(-1).src = lastToken.text;\n        } else if (!this.tokens.links[token.tag]) {\n          this.tokens.links[token.tag] = {\n            href: token.href,\n            title: token.title\n          };\n        }\n        continue;\n      }\n      if (token = this.tokenizer.table(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      if (token = this.tokenizer.lheading(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      let cutSrc = src;\n      if (this.options.extensions?.startBlock) {\n        let startIndex = Infinity;\n        const tempSrc = src.slice(1);\n        let tempStart;\n        this.options.extensions.startBlock.forEach((getStartIndex) => {\n          tempStart = getStartIndex.call({ lexer: this }, tempSrc);\n          if (typeof tempStart === \"number\" && tempStart >= 0) {\n            startIndex = Math.min(startIndex, tempStart);\n          }\n        });\n        if (startIndex < Infinity && startIndex >= 0) {\n          cutSrc = src.substring(0, startIndex + 1);\n        }\n      }\n      if (this.state.top && (token = this.tokenizer.paragraph(cutSrc))) {\n        const lastToken = tokens.at(-1);\n        if (lastParagraphClipped && lastToken?.type === \"paragraph\") {\n          lastToken.raw += \"\\n\" + token.raw;\n          lastToken.text += \"\\n\" + token.text;\n          this.inlineQueue.pop();\n          this.inlineQueue.at(-1).src = lastToken.text;\n        } else {\n          tokens.push(token);\n        }\n        lastParagraphClipped = cutSrc.length !== src.length;\n        src = src.substring(token.raw.length);\n        continue;\n      }\n      if (token = this.tokenizer.text(src)) {\n        src = src.substring(token.raw.length);\n        const lastToken = tokens.at(-1);\n        if (lastToken?.type === \"text\") {\n          lastToken.raw += \"\\n\" + token.raw;\n          lastToken.text += \"\\n\" + token.text;\n          this.inlineQueue.pop();\n          this.inlineQueue.at(-1).src = lastToken.text;\n        } else {\n          tokens.push(token);\n        }\n        continue;\n      }\n      if (src) {\n        const errMsg = \"Infinite loop on byte: \" + src.charCodeAt(0);\n        if (this.options.silent) {\n          console.error(errMsg);\n          break;\n        } else {\n          throw new Error(errMsg);\n        }\n      }\n    }\n    this.state.top = true;\n    return tokens;\n  }\n  inline(src, tokens = []) {\n    this.inlineQueue.push({ src, tokens });\n    return tokens;\n  }\n  /**\n   * Lexing/Compiling\n   */\n  inlineTokens(src, tokens = []) {\n    let maskedSrc = src;\n    let match = null;\n    if (this.tokens.links) {\n      const links = Object.keys(this.tokens.links);\n      if (links.length > 0) {\n        while ((match = this.tokenizer.rules.inline.reflinkSearch.exec(maskedSrc)) != null) {\n          if (links.includes(match[0].slice(match[0].lastIndexOf(\"[\") + 1, -1))) {\n            maskedSrc = maskedSrc.slice(0, match.index) + \"[\" + \"a\".repeat(match[0].length - 2) + \"]\" + maskedSrc.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex);\n          }\n        }\n      }\n    }\n    while ((match = this.tokenizer.rules.inline.anyPunctuation.exec(maskedSrc)) != null) {\n      maskedSrc = maskedSrc.slice(0, match.index) + \"++\" + maskedSrc.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);\n    }\n    while ((match = this.tokenizer.rules.inline.blockSkip.exec(maskedSrc)) != null) {\n      maskedSrc = maskedSrc.slice(0, match.index) + \"[\" + \"a\".repeat(match[0].length - 2) + \"]\" + maskedSrc.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);\n    }\n    let keepPrevChar = false;\n    let prevChar = \"\";\n    while (src) {\n      if (!keepPrevChar) {\n        prevChar = \"\";\n      }\n      keepPrevChar = false;\n      let token;\n      if (this.options.extensions?.inline?.some((extTokenizer) => {\n        if (token = extTokenizer.call({ lexer: this }, src, tokens)) {\n          src = src.substring(token.raw.length);\n          tokens.push(token);\n          return true;\n        }\n        return false;\n      })) {\n        continue;\n      }\n      if (token = this.tokenizer.escape(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      if (token = this.tokenizer.tag(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      if (token = this.tokenizer.link(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      if (token = this.tokenizer.reflink(src, this.tokens.links)) {\n        src = src.substring(token.raw.length);\n        const lastToken = tokens.at(-1);\n        if (token.type === \"text\" && lastToken?.type === \"text\") {\n          lastToken.raw += token.raw;\n          lastToken.text += token.text;\n        } else {\n          tokens.push(token);\n        }\n        continue;\n      }\n      if (token = this.tokenizer.emStrong(src, maskedSrc, prevChar)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      if (token = this.tokenizer.codespan(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      if (token = this.tokenizer.br(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      if (token = this.tokenizer.del(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      if (token = this.tokenizer.autolink(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      if (!this.state.inLink && (token = this.tokenizer.url(src))) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      let cutSrc = src;\n      if (this.options.extensions?.startInline) {\n        let startIndex = Infinity;\n        const tempSrc = src.slice(1);\n        let tempStart;\n        this.options.extensions.startInline.forEach((getStartIndex) => {\n          tempStart = getStartIndex.call({ lexer: this }, tempSrc);\n          if (typeof tempStart === \"number\" && tempStart >= 0) {\n            startIndex = Math.min(startIndex, tempStart);\n          }\n        });\n        if (startIndex < Infinity && startIndex >= 0) {\n          cutSrc = src.substring(0, startIndex + 1);\n        }\n      }\n      if (token = this.tokenizer.inlineText(cutSrc)) {\n        src = src.substring(token.raw.length);\n        if (token.raw.slice(-1) !== \"_\") {\n          prevChar = token.raw.slice(-1);\n        }\n        keepPrevChar = true;\n        const lastToken = tokens.at(-1);\n        if (lastToken?.type === \"text\") {\n          lastToken.raw += token.raw;\n          lastToken.text += token.text;\n        } else {\n          tokens.push(token);\n        }\n        continue;\n      }\n      if (src) {\n        const errMsg = \"Infinite loop on byte: \" + src.charCodeAt(0);\n        if (this.options.silent) {\n          console.error(errMsg);\n          break;\n        } else {\n          throw new Error(errMsg);\n        }\n      }\n    }\n    return tokens;\n  }\n};\n\n// src/Renderer.ts\nvar _Renderer = class {\n  options;\n  parser;\n  // set by the parser\n  constructor(options2) {\n    this.options = options2 || _defaults;\n  }\n  space(token) {\n    return \"\";\n  }\n  code({ text, lang, escaped }) {\n    const langString = (lang || \"\").match(other.notSpaceStart)?.[0];\n    const code = text.replace(other.endingNewline, \"\") + \"\\n\";\n    if (!langString) {\n      return \"<pre><code>\" + (escaped ? code : escape2(code, true)) + \"</code></pre>\\n\";\n    }\n    return '<pre><code class=\"language-' + escape2(langString) + '\">' + (escaped ? code : escape2(code, true)) + \"</code></pre>\\n\";\n  }\n  blockquote({ tokens }) {\n    const body = this.parser.parse(tokens);\n    return `<blockquote>\n${body}</blockquote>\n`;\n  }\n  html({ text }) {\n    return text;\n  }\n  heading({ tokens, depth }) {\n    return `<h${depth}>${this.parser.parseInline(tokens)}</h${depth}>\n`;\n  }\n  hr(token) {\n    return \"<hr>\\n\";\n  }\n  list(token) {\n    const ordered = token.ordered;\n    const start = token.start;\n    let body = \"\";\n    for (let j = 0; j < token.items.length; j++) {\n      const item = token.items[j];\n      body += this.listitem(item);\n    }\n    const type = ordered ? \"ol\" : \"ul\";\n    const startAttr = ordered && start !== 1 ? ' start=\"' + start + '\"' : \"\";\n    return \"<\" + type + startAttr + \">\\n\" + body + \"</\" + type + \">\\n\";\n  }\n  listitem(item) {\n    let itemBody = \"\";\n    if (item.task) {\n      const checkbox = this.checkbox({ checked: !!item.checked });\n      if (item.loose) {\n        if (item.tokens[0]?.type === \"paragraph\") {\n          item.tokens[0].text = checkbox + \" \" + item.tokens[0].text;\n          if (item.tokens[0].tokens && item.tokens[0].tokens.length > 0 && item.tokens[0].tokens[0].type === \"text\") {\n            item.tokens[0].tokens[0].text = checkbox + \" \" + escape2(item.tokens[0].tokens[0].text);\n            item.tokens[0].tokens[0].escaped = true;\n          }\n        } else {\n          item.tokens.unshift({\n            type: \"text\",\n            raw: checkbox + \" \",\n            text: checkbox + \" \",\n            escaped: true\n          });\n        }\n      } else {\n        itemBody += checkbox + \" \";\n      }\n    }\n    itemBody += this.parser.parse(item.tokens, !!item.loose);\n    return `<li>${itemBody}</li>\n`;\n  }\n  checkbox({ checked }) {\n    return \"<input \" + (checked ? 'checked=\"\" ' : \"\") + 'disabled=\"\" type=\"checkbox\">';\n  }\n  paragraph({ tokens }) {\n    return `<p>${this.parser.parseInline(tokens)}</p>\n`;\n  }\n  table(token) {\n    let header = \"\";\n    let cell = \"\";\n    for (let j = 0; j < token.header.length; j++) {\n      cell += this.tablecell(token.header[j]);\n    }\n    header += this.tablerow({ text: cell });\n    let body = \"\";\n    for (let j = 0; j < token.rows.length; j++) {\n      const row = token.rows[j];\n      cell = \"\";\n      for (let k = 0; k < row.length; k++) {\n        cell += this.tablecell(row[k]);\n      }\n      body += this.tablerow({ text: cell });\n    }\n    if (body) body = `<tbody>${body}</tbody>`;\n    return \"<table>\\n<thead>\\n\" + header + \"</thead>\\n\" + body + \"</table>\\n\";\n  }\n  tablerow({ text }) {\n    return `<tr>\n${text}</tr>\n`;\n  }\n  tablecell(token) {\n    const content = this.parser.parseInline(token.tokens);\n    const type = token.header ? \"th\" : \"td\";\n    const tag2 = token.align ? `<${type} align=\"${token.align}\">` : `<${type}>`;\n    return tag2 + content + `</${type}>\n`;\n  }\n  /**\n   * span level renderer\n   */\n  strong({ tokens }) {\n    return `<strong>${this.parser.parseInline(tokens)}</strong>`;\n  }\n  em({ tokens }) {\n    return `<em>${this.parser.parseInline(tokens)}</em>`;\n  }\n  codespan({ text }) {\n    return `<code>${escape2(text, true)}</code>`;\n  }\n  br(token) {\n    return \"<br>\";\n  }\n  del({ tokens }) {\n    return `<del>${this.parser.parseInline(tokens)}</del>`;\n  }\n  link({ href, title, tokens }) {\n    const text = this.parser.parseInline(tokens);\n    const cleanHref = cleanUrl(href);\n    if (cleanHref === null) {\n      return text;\n    }\n    href = cleanHref;\n    let out = '<a href=\"' + href + '\"';\n    if (title) {\n      out += ' title=\"' + escape2(title) + '\"';\n    }\n    out += \">\" + text + \"</a>\";\n    return out;\n  }\n  image({ href, title, text, tokens }) {\n    if (tokens) {\n      text = this.parser.parseInline(tokens, this.parser.textRenderer);\n    }\n    const cleanHref = cleanUrl(href);\n    if (cleanHref === null) {\n      return escape2(text);\n    }\n    href = cleanHref;\n    let out = `<img src=\"${href}\" alt=\"${text}\"`;\n    if (title) {\n      out += ` title=\"${escape2(title)}\"`;\n    }\n    out += \">\";\n    return out;\n  }\n  text(token) {\n    return \"tokens\" in token && token.tokens ? this.parser.parseInline(token.tokens) : \"escaped\" in token && token.escaped ? token.text : escape2(token.text);\n  }\n};\n\n// src/TextRenderer.ts\nvar _TextRenderer = class {\n  // no need for block level renderers\n  strong({ text }) {\n    return text;\n  }\n  em({ text }) {\n    return text;\n  }\n  codespan({ text }) {\n    return text;\n  }\n  del({ text }) {\n    return text;\n  }\n  html({ text }) {\n    return text;\n  }\n  text({ text }) {\n    return text;\n  }\n  link({ text }) {\n    return \"\" + text;\n  }\n  image({ text }) {\n    return \"\" + text;\n  }\n  br() {\n    return \"\";\n  }\n};\n\n// src/Parser.ts\nvar _Parser = class __Parser {\n  options;\n  renderer;\n  textRenderer;\n  constructor(options2) {\n    this.options = options2 || _defaults;\n    this.options.renderer = this.options.renderer || new _Renderer();\n    this.renderer = this.options.renderer;\n    this.renderer.options = this.options;\n    this.renderer.parser = this;\n    this.textRenderer = new _TextRenderer();\n  }\n  /**\n   * Static Parse Method\n   */\n  static parse(tokens, options2) {\n    const parser2 = new __Parser(options2);\n    return parser2.parse(tokens);\n  }\n  /**\n   * Static Parse Inline Method\n   */\n  static parseInline(tokens, options2) {\n    const parser2 = new __Parser(options2);\n    return parser2.parseInline(tokens);\n  }\n  /**\n   * Parse Loop\n   */\n  parse(tokens, top = true) {\n    let out = \"\";\n    for (let i = 0; i < tokens.length; i++) {\n      const anyToken = tokens[i];\n      if (this.options.extensions?.renderers?.[anyToken.type]) {\n        const genericToken = anyToken;\n        const ret = this.options.extensions.renderers[genericToken.type].call({ parser: this }, genericToken);\n        if (ret !== false || ![\"space\", \"hr\", \"heading\", \"code\", \"table\", \"blockquote\", \"list\", \"html\", \"paragraph\", \"text\"].includes(genericToken.type)) {\n          out += ret || \"\";\n          continue;\n        }\n      }\n      const token = anyToken;\n      switch (token.type) {\n        case \"space\": {\n          out += this.renderer.space(token);\n          continue;\n        }\n        case \"hr\": {\n          out += this.renderer.hr(token);\n          continue;\n        }\n        case \"heading\": {\n          out += this.renderer.heading(token);\n          continue;\n        }\n        case \"code\": {\n          out += this.renderer.code(token);\n          continue;\n        }\n        case \"table\": {\n          out += this.renderer.table(token);\n          continue;\n        }\n        case \"blockquote\": {\n          out += this.renderer.blockquote(token);\n          continue;\n        }\n        case \"list\": {\n          out += this.renderer.list(token);\n          continue;\n        }\n        case \"html\": {\n          out += this.renderer.html(token);\n          continue;\n        }\n        case \"paragraph\": {\n          out += this.renderer.paragraph(token);\n          continue;\n        }\n        case \"text\": {\n          let textToken = token;\n          let body = this.renderer.text(textToken);\n          while (i + 1 < tokens.length && tokens[i + 1].type === \"text\") {\n            textToken = tokens[++i];\n            body += \"\\n\" + this.renderer.text(textToken);\n          }\n          if (top) {\n            out += this.renderer.paragraph({\n              type: \"paragraph\",\n              raw: body,\n              text: body,\n              tokens: [{ type: \"text\", raw: body, text: body, escaped: true }]\n            });\n          } else {\n            out += body;\n          }\n          continue;\n        }\n        default: {\n          const errMsg = 'Token with \"' + token.type + '\" type was not found.';\n          if (this.options.silent) {\n            console.error(errMsg);\n            return \"\";\n          } else {\n            throw new Error(errMsg);\n          }\n        }\n      }\n    }\n    return out;\n  }\n  /**\n   * Parse Inline Tokens\n   */\n  parseInline(tokens, renderer = this.renderer) {\n    let out = \"\";\n    for (let i = 0; i < tokens.length; i++) {\n      const anyToken = tokens[i];\n      if (this.options.extensions?.renderers?.[anyToken.type]) {\n        const ret = this.options.extensions.renderers[anyToken.type].call({ parser: this }, anyToken);\n        if (ret !== false || ![\"escape\", \"html\", \"link\", \"image\", \"strong\", \"em\", \"codespan\", \"br\", \"del\", \"text\"].includes(anyToken.type)) {\n          out += ret || \"\";\n          continue;\n        }\n      }\n      const token = anyToken;\n      switch (token.type) {\n        case \"escape\": {\n          out += renderer.text(token);\n          break;\n        }\n        case \"html\": {\n          out += renderer.html(token);\n          break;\n        }\n        case \"link\": {\n          out += renderer.link(token);\n          break;\n        }\n        case \"image\": {\n          out += renderer.image(token);\n          break;\n        }\n        case \"strong\": {\n          out += renderer.strong(token);\n          break;\n        }\n        case \"em\": {\n          out += renderer.em(token);\n          break;\n        }\n        case \"codespan\": {\n          out += renderer.codespan(token);\n          break;\n        }\n        case \"br\": {\n          out += renderer.br(token);\n          break;\n        }\n        case \"del\": {\n          out += renderer.del(token);\n          break;\n        }\n        case \"text\": {\n          out += renderer.text(token);\n          break;\n        }\n        default: {\n          const errMsg = 'Token with \"' + token.type + '\" type was not found.';\n          if (this.options.silent) {\n            console.error(errMsg);\n            return \"\";\n          } else {\n            throw new Error(errMsg);\n          }\n        }\n      }\n    }\n    return out;\n  }\n};\n\n// src/Hooks.ts\nvar _Hooks = class {\n  options;\n  block;\n  constructor(options2) {\n    this.options = options2 || _defaults;\n  }\n  static passThroughHooks = /* @__PURE__ */ new Set([\n    \"preprocess\",\n    \"postprocess\",\n    \"processAllTokens\"\n  ]);\n  /**\n   * Process markdown before marked\n   */\n  preprocess(markdown) {\n    return markdown;\n  }\n  /**\n   * Process HTML after marked is finished\n   */\n  postprocess(html2) {\n    return html2;\n  }\n  /**\n   * Process all tokens before walk tokens\n   */\n  processAllTokens(tokens) {\n    return tokens;\n  }\n  /**\n   * Provide function to tokenize markdown\n   */\n  provideLexer() {\n    return this.block ? _Lexer.lex : _Lexer.lexInline;\n  }\n  /**\n   * Provide function to parse tokens\n   */\n  provideParser() {\n    return this.block ? _Parser.parse : _Parser.parseInline;\n  }\n};\n\n// src/Instance.ts\nvar Marked = class {\n  defaults = _getDefaults();\n  options = this.setOptions;\n  parse = this.parseMarkdown(true);\n  parseInline = this.parseMarkdown(false);\n  Parser = _Parser;\n  Renderer = _Renderer;\n  TextRenderer = _TextRenderer;\n  Lexer = _Lexer;\n  Tokenizer = _Tokenizer;\n  Hooks = _Hooks;\n  constructor(...args) {\n    this.use(...args);\n  }\n  /**\n   * Run callback for every token\n   */\n  walkTokens(tokens, callback) {\n    let values = [];\n    for (const token of tokens) {\n      values = values.concat(callback.call(this, token));\n      switch (token.type) {\n        case \"table\": {\n          const tableToken = token;\n          for (const cell of tableToken.header) {\n            values = values.concat(this.walkTokens(cell.tokens, callback));\n          }\n          for (const row of tableToken.rows) {\n            for (const cell of row) {\n              values = values.concat(this.walkTokens(cell.tokens, callback));\n            }\n          }\n          break;\n        }\n        case \"list\": {\n          const listToken = token;\n          values = values.concat(this.walkTokens(listToken.items, callback));\n          break;\n        }\n        default: {\n          const genericToken = token;\n          if (this.defaults.extensions?.childTokens?.[genericToken.type]) {\n            this.defaults.extensions.childTokens[genericToken.type].forEach((childTokens) => {\n              const tokens2 = genericToken[childTokens].flat(Infinity);\n              values = values.concat(this.walkTokens(tokens2, callback));\n            });\n          } else if (genericToken.tokens) {\n            values = values.concat(this.walkTokens(genericToken.tokens, callback));\n          }\n        }\n      }\n    }\n    return values;\n  }\n  use(...args) {\n    const extensions = this.defaults.extensions || { renderers: {}, childTokens: {} };\n    args.forEach((pack) => {\n      const opts = { ...pack };\n      opts.async = this.defaults.async || opts.async || false;\n      if (pack.extensions) {\n        pack.extensions.forEach((ext) => {\n          if (!ext.name) {\n            throw new Error(\"extension name required\");\n          }\n          if (\"renderer\" in ext) {\n            const prevRenderer = extensions.renderers[ext.name];\n            if (prevRenderer) {\n              extensions.renderers[ext.name] = function(...args2) {\n                let ret = ext.renderer.apply(this, args2);\n                if (ret === false) {\n                  ret = prevRenderer.apply(this, args2);\n                }\n                return ret;\n              };\n            } else {\n              extensions.renderers[ext.name] = ext.renderer;\n            }\n          }\n          if (\"tokenizer\" in ext) {\n            if (!ext.level || ext.level !== \"block\" && ext.level !== \"inline\") {\n              throw new Error(\"extension level must be 'block' or 'inline'\");\n            }\n            const extLevel = extensions[ext.level];\n            if (extLevel) {\n              extLevel.unshift(ext.tokenizer);\n            } else {\n              extensions[ext.level] = [ext.tokenizer];\n            }\n            if (ext.start) {\n              if (ext.level === \"block\") {\n                if (extensions.startBlock) {\n                  extensions.startBlock.push(ext.start);\n                } else {\n                  extensions.startBlock = [ext.start];\n                }\n              } else if (ext.level === \"inline\") {\n                if (extensions.startInline) {\n                  extensions.startInline.push(ext.start);\n                } else {\n                  extensions.startInline = [ext.start];\n                }\n              }\n            }\n          }\n          if (\"childTokens\" in ext && ext.childTokens) {\n            extensions.childTokens[ext.name] = ext.childTokens;\n          }\n        });\n        opts.extensions = extensions;\n      }\n      if (pack.renderer) {\n        const renderer = this.defaults.renderer || new _Renderer(this.defaults);\n        for (const prop in pack.renderer) {\n          if (!(prop in renderer)) {\n            throw new Error(`renderer '${prop}' does not exist`);\n          }\n          if ([\"options\", \"parser\"].includes(prop)) {\n            continue;\n          }\n          const rendererProp = prop;\n          const rendererFunc = pack.renderer[rendererProp];\n          const prevRenderer = renderer[rendererProp];\n          renderer[rendererProp] = (...args2) => {\n            let ret = rendererFunc.apply(renderer, args2);\n            if (ret === false) {\n              ret = prevRenderer.apply(renderer, args2);\n            }\n            return ret || \"\";\n          };\n        }\n        opts.renderer = renderer;\n      }\n      if (pack.tokenizer) {\n        const tokenizer = this.defaults.tokenizer || new _Tokenizer(this.defaults);\n        for (const prop in pack.tokenizer) {\n          if (!(prop in tokenizer)) {\n            throw new Error(`tokenizer '${prop}' does not exist`);\n          }\n          if ([\"options\", \"rules\", \"lexer\"].includes(prop)) {\n            continue;\n          }\n          const tokenizerProp = prop;\n          const tokenizerFunc = pack.tokenizer[tokenizerProp];\n          const prevTokenizer = tokenizer[tokenizerProp];\n          tokenizer[tokenizerProp] = (...args2) => {\n            let ret = tokenizerFunc.apply(tokenizer, args2);\n            if (ret === false) {\n              ret = prevTokenizer.apply(tokenizer, args2);\n            }\n            return ret;\n          };\n        }\n        opts.tokenizer = tokenizer;\n      }\n      if (pack.hooks) {\n        const hooks = this.defaults.hooks || new _Hooks();\n        for (const prop in pack.hooks) {\n          if (!(prop in hooks)) {\n            throw new Error(`hook '${prop}' does not exist`);\n          }\n          if ([\"options\", \"block\"].includes(prop)) {\n            continue;\n          }\n          const hooksProp = prop;\n          const hooksFunc = pack.hooks[hooksProp];\n          const prevHook = hooks[hooksProp];\n          if (_Hooks.passThroughHooks.has(prop)) {\n            hooks[hooksProp] = (arg) => {\n              if (this.defaults.async) {\n                return Promise.resolve(hooksFunc.call(hooks, arg)).then((ret2) => {\n                  return prevHook.call(hooks, ret2);\n                });\n              }\n              const ret = hooksFunc.call(hooks, arg);\n              return prevHook.call(hooks, ret);\n            };\n          } else {\n            hooks[hooksProp] = (...args2) => {\n              let ret = hooksFunc.apply(hooks, args2);\n              if (ret === false) {\n                ret = prevHook.apply(hooks, args2);\n              }\n              return ret;\n            };\n          }\n        }\n        opts.hooks = hooks;\n      }\n      if (pack.walkTokens) {\n        const walkTokens2 = this.defaults.walkTokens;\n        const packWalktokens = pack.walkTokens;\n        opts.walkTokens = function(token) {\n          let values = [];\n          values.push(packWalktokens.call(this, token));\n          if (walkTokens2) {\n            values = values.concat(walkTokens2.call(this, token));\n          }\n          return values;\n        };\n      }\n      this.defaults = { ...this.defaults, ...opts };\n    });\n    return this;\n  }\n  setOptions(opt) {\n    this.defaults = { ...this.defaults, ...opt };\n    return this;\n  }\n  lexer(src, options2) {\n    return _Lexer.lex(src, options2 ?? this.defaults);\n  }\n  parser(tokens, options2) {\n    return _Parser.parse(tokens, options2 ?? this.defaults);\n  }\n  parseMarkdown(blockType) {\n    const parse2 = (src, options2) => {\n      const origOpt = { ...options2 };\n      const opt = { ...this.defaults, ...origOpt };\n      const throwError = this.onError(!!opt.silent, !!opt.async);\n      if (this.defaults.async === true && origOpt.async === false) {\n        return throwError(new Error(\"marked(): The async option was set to true by an extension. Remove async: false from the parse options object to return a Promise.\"));\n      }\n      if (typeof src === \"undefined\" || src === null) {\n        return throwError(new Error(\"marked(): input parameter is undefined or null\"));\n      }\n      if (typeof src !== \"string\") {\n        return throwError(new Error(\"marked(): input parameter is of type \" + Object.prototype.toString.call(src) + \", string expected\"));\n      }\n      if (opt.hooks) {\n        opt.hooks.options = opt;\n        opt.hooks.block = blockType;\n      }\n      const lexer2 = opt.hooks ? opt.hooks.provideLexer() : blockType ? _Lexer.lex : _Lexer.lexInline;\n      const parser2 = opt.hooks ? opt.hooks.provideParser() : blockType ? _Parser.parse : _Parser.parseInline;\n      if (opt.async) {\n        return Promise.resolve(opt.hooks ? opt.hooks.preprocess(src) : src).then((src2) => lexer2(src2, opt)).then((tokens) => opt.hooks ? opt.hooks.processAllTokens(tokens) : tokens).then((tokens) => opt.walkTokens ? Promise.all(this.walkTokens(tokens, opt.walkTokens)).then(() => tokens) : tokens).then((tokens) => parser2(tokens, opt)).then((html2) => opt.hooks ? opt.hooks.postprocess(html2) : html2).catch(throwError);\n      }\n      try {\n        if (opt.hooks) {\n          src = opt.hooks.preprocess(src);\n        }\n        let tokens = lexer2(src, opt);\n        if (opt.hooks) {\n          tokens = opt.hooks.processAllTokens(tokens);\n        }\n        if (opt.walkTokens) {\n          this.walkTokens(tokens, opt.walkTokens);\n        }\n        let html2 = parser2(tokens, opt);\n        if (opt.hooks) {\n          html2 = opt.hooks.postprocess(html2);\n        }\n        return html2;\n      } catch (e) {\n        return throwError(e);\n      }\n    };\n    return parse2;\n  }\n  onError(silent, async) {\n    return (e) => {\n      e.message += \"\\nPlease report this to https://github.com/markedjs/marked.\";\n      if (silent) {\n        const msg = \"<p>An error occurred:</p><pre>\" + escape2(e.message + \"\", true) + \"</pre>\";\n        if (async) {\n          return Promise.resolve(msg);\n        }\n        return msg;\n      }\n      if (async) {\n        return Promise.reject(e);\n      }\n      throw e;\n    };\n  }\n};\n\n// src/marked.ts\nvar markedInstance = new Marked();\nfunction marked(src, opt) {\n  return markedInstance.parse(src, opt);\n}\nmarked.options = marked.setOptions = function(options2) {\n  markedInstance.setOptions(options2);\n  marked.defaults = markedInstance.defaults;\n  changeDefaults(marked.defaults);\n  return marked;\n};\nmarked.getDefaults = _getDefaults;\nmarked.defaults = _defaults;\nmarked.use = function(...args) {\n  markedInstance.use(...args);\n  marked.defaults = markedInstance.defaults;\n  changeDefaults(marked.defaults);\n  return marked;\n};\nmarked.walkTokens = function(tokens, callback) {\n  return markedInstance.walkTokens(tokens, callback);\n};\nmarked.parseInline = markedInstance.parseInline;\nmarked.Parser = _Parser;\nmarked.parser = _Parser.parse;\nmarked.Renderer = _Renderer;\nmarked.TextRenderer = _TextRenderer;\nmarked.Lexer = _Lexer;\nmarked.lexer = _Lexer.lex;\nmarked.Tokenizer = _Tokenizer;\nmarked.Hooks = _Hooks;\nmarked.parse = marked;\nvar options = marked.options;\nvar setOptions = marked.setOptions;\nvar use = marked.use;\nvar walkTokens = marked.walkTokens;\nvar parseInline = marked.parseInline;\nvar parse = marked;\nvar parser = _Parser.parse;\nvar lexer = _Lexer.lex;\nexport {\n  _Hooks as Hooks,\n  _Lexer as Lexer,\n  Marked,\n  _Parser as Parser,\n  _Renderer as Renderer,\n  _TextRenderer as TextRenderer,\n  _Tokenizer as Tokenizer,\n  _defaults as defaults,\n  _getDefaults as getDefaults,\n  lexer,\n  marked,\n  options,\n  parse,\n  parseInline,\n  parser,\n  setOptions,\n  use,\n  walkTokens\n};\n\n", "import * as i0 from '@angular/core';\nimport { computed, ChangeDetectionStrategy, Component, InjectionToken, Pipe, PLATFORM_ID, Inject, Optional, Injectable, EventEmitter, Output, Input, SecurityContext, NgModule } from '@angular/core';\nimport { toSignal } from '@angular/core/rxjs-interop';\nimport { Subject, merge, of, timer } from 'rxjs';\nimport { switchMap, mapTo, distinctUntilChanged, shareReplay, map, takeUntil, first } from 'rxjs/operators';\nimport { isPlatformBrowser } from '@angular/common';\nimport { Renderer, marked } from 'marked';\nconst _c0 = [\"*\"];\nexport { Renderer as MarkedRenderer } from 'marked';\nimport * as i1 from '@angular/common/http';\nimport * as i2 from '@angular/platform-browser';\nconst BUTTON_TEXT_COPY = 'Copy';\nconst BUTTON_TEXT_COPIED = 'Copied';\nclass ClipboardButtonComponent {\n  constructor() {\n    this._buttonClick$ = new Subject();\n    this.copied = toSignal(this._buttonClick$.pipe(switchMap(() => merge(of(true), timer(3000).pipe(mapTo(false)))), distinctUntilChanged(), shareReplay(1)));\n    this.copiedText = computed(() => this.copied() ? BUTTON_TEXT_COPIED : BUTTON_TEXT_COPY);\n  }\n  onCopyToClipboardClick() {\n    this._buttonClick$.next();\n  }\n  static {\n    this.ɵfac = function ClipboardButtonComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ClipboardButtonComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: ClipboardButtonComponent,\n      selectors: [[\"markdown-clipboard\"]],\n      decls: 2,\n      vars: 3,\n      consts: [[1, \"markdown-clipboard-button\", 3, \"click\"]],\n      template: function ClipboardButtonComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"button\", 0);\n          i0.ɵɵlistener(\"click\", function ClipboardButtonComponent_Template_button_click_0_listener() {\n            return ctx.onCopyToClipboardClick();\n          });\n          i0.ɵɵtext(1);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"copied\", ctx.copied());\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate(ctx.copiedText());\n        }\n      },\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ClipboardButtonComponent, [{\n    type: Component,\n    args: [{\n      selector: 'markdown-clipboard',\n      template: `\n    <button\n      class=\"markdown-clipboard-button\"\n      [class.copied]=\"copied()\"\n      (click)=\"onCopyToClipboardClick()\"\n    >{{ copiedText() }}</button>\n  `,\n      changeDetection: ChangeDetectionStrategy.OnPush\n    }]\n  }], null, null);\n})();\nconst CLIPBOARD_OPTIONS = new InjectionToken('CLIPBOARD_OPTIONS');\n\n/* eslint-disable */\nclass KatexSpecificOptions {}\nclass LanguagePipe {\n  transform(value, language) {\n    if (value == null) {\n      value = '';\n    }\n    if (language == null) {\n      language = '';\n    }\n    if (typeof value !== 'string') {\n      console.error(`LanguagePipe has been invoked with an invalid value type [${typeof value}]`);\n      return value;\n    }\n    if (typeof language !== 'string') {\n      console.error(`LanguagePipe has been invoked with an invalid parameter [${typeof language}]`);\n      return value;\n    }\n    return '```' + language + '\\n' + value + '\\n```';\n  }\n  static {\n    this.ɵfac = function LanguagePipe_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || LanguagePipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n      name: \"language\",\n      type: LanguagePipe,\n      pure: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LanguagePipe, [{\n    type: Pipe,\n    args: [{\n      name: 'language'\n    }]\n  }], null, null);\n})();\nvar PrismPlugin;\n(function (PrismPlugin) {\n  PrismPlugin[\"CommandLine\"] = \"command-line\";\n  PrismPlugin[\"LineHighlight\"] = \"line-highlight\";\n  PrismPlugin[\"LineNumbers\"] = \"line-numbers\";\n})(PrismPlugin || (PrismPlugin = {}));\nconst MARKED_EXTENSIONS = new InjectionToken('MARKED_EXTENSIONS');\nconst MARKED_OPTIONS = new InjectionToken('MARKED_OPTIONS');\nconst MERMAID_OPTIONS = new InjectionToken('MERMAID_OPTIONS');\nconst errorJoyPixelsNotLoaded = '[ngx-markdown] When using the `emoji` attribute you *have to* include Emoji-Toolkit files to `angular.json` or use imports. See README for more information';\nconst errorKatexNotLoaded = '[ngx-markdown] When using the `katex` attribute you *have to* include KaTeX files to `angular.json` or use imports. See README for more information';\nconst errorMermaidNotLoaded = '[ngx-markdown] When using the `mermaid` attribute you *have to* include Mermaid files to `angular.json` or use imports. See README for more information';\nconst errorClipboardNotLoaded = '[ngx-markdown] When using the `clipboard` attribute you *have to* include Clipboard files to `angular.json` or use imports. See README for more information';\nconst errorClipboardViewContainerRequired = '[ngx-markdown] When using the `clipboard` attribute you *have to* provide the `viewContainerRef` parameter to `MarkdownService.render()` function';\nconst errorSrcWithoutHttpClient = '[ngx-markdown] When using the `src` attribute you *have to* pass the `HttpClient` as a parameter of the `forRoot` method. See README for more information';\nconst SECURITY_CONTEXT = new InjectionToken('SECURITY_CONTEXT');\nclass ExtendedRenderer extends Renderer {\n  constructor() {\n    super(...arguments);\n    this.ɵNgxMarkdownRendererExtendedForExtensions = false;\n    this.ɵNgxMarkdownRendererExtendedForMermaid = false;\n  }\n}\nclass MarkdownService {\n  get options() {\n    return this._options;\n  }\n  set options(value) {\n    this._options = {\n      ...this.DEFAULT_MARKED_OPTIONS,\n      ...value\n    };\n  }\n  get renderer() {\n    return this.options.renderer;\n  }\n  set renderer(value) {\n    this.options.renderer = value;\n  }\n  constructor(clipboardOptions, extensions, options, mermaidOptions, platform, securityContext, http, sanitizer) {\n    this.clipboardOptions = clipboardOptions;\n    this.extensions = extensions;\n    this.mermaidOptions = mermaidOptions;\n    this.platform = platform;\n    this.securityContext = securityContext;\n    this.http = http;\n    this.sanitizer = sanitizer;\n    this.DEFAULT_MARKED_OPTIONS = {\n      renderer: new Renderer()\n    };\n    this.DEFAULT_KATEX_OPTIONS = {\n      delimiters: [{\n        left: '$$',\n        right: '$$',\n        display: true\n      }, {\n        left: '$',\n        right: '$',\n        display: false\n      }, {\n        left: '\\\\(',\n        right: '\\\\)',\n        display: false\n      }, {\n        left: '\\\\begin{equation}',\n        right: '\\\\end{equation}',\n        display: true\n      }, {\n        left: '\\\\begin{align}',\n        right: '\\\\end{align}',\n        display: true\n      }, {\n        left: '\\\\begin{alignat}',\n        right: '\\\\end{alignat}',\n        display: true\n      }, {\n        left: '\\\\begin{gather}',\n        right: '\\\\end{gather}',\n        display: true\n      }, {\n        left: '\\\\begin{CD}',\n        right: '\\\\end{CD}',\n        display: true\n      }, {\n        left: '\\\\[',\n        right: '\\\\]',\n        display: true\n      }]\n    };\n    this.DEFAULT_MERMAID_OPTIONS = {\n      startOnLoad: false\n    };\n    this.DEFAULT_CLIPBOARD_OPTIONS = {\n      buttonComponent: undefined\n    };\n    this.DEFAULT_PARSE_OPTIONS = {\n      decodeHtml: false,\n      inline: false,\n      emoji: false,\n      mermaid: false,\n      markedOptions: undefined,\n      disableSanitizer: false\n    };\n    this.DEFAULT_RENDER_OPTIONS = {\n      clipboard: false,\n      clipboardOptions: undefined,\n      katex: false,\n      katexOptions: undefined,\n      mermaid: false,\n      mermaidOptions: undefined\n    };\n    this._reload$ = new Subject();\n    this.reload$ = this._reload$.asObservable();\n    this.options = options;\n  }\n  parse(markdown, parseOptions = this.DEFAULT_PARSE_OPTIONS) {\n    const {\n      decodeHtml,\n      inline,\n      emoji,\n      mermaid,\n      disableSanitizer\n    } = parseOptions;\n    const markedOptions = {\n      ...this.options,\n      ...parseOptions.markedOptions\n    };\n    const renderer = markedOptions.renderer || this.renderer || new Renderer();\n    if (this.extensions) {\n      this.renderer = this.extendsRendererForExtensions(renderer);\n    }\n    if (mermaid) {\n      this.renderer = this.extendsRendererForMermaid(renderer);\n    }\n    const trimmed = this.trimIndentation(markdown);\n    const decoded = decodeHtml ? this.decodeHtml(trimmed) : trimmed;\n    const emojified = emoji ? this.parseEmoji(decoded) : decoded;\n    const marked = this.parseMarked(emojified, markedOptions, inline);\n    const sanitized = disableSanitizer ? marked : this.sanitizer.sanitize(this.securityContext, marked);\n    return sanitized || '';\n  }\n  render(element, options = this.DEFAULT_RENDER_OPTIONS, viewContainerRef) {\n    const {\n      clipboard,\n      clipboardOptions,\n      katex,\n      katexOptions,\n      mermaid,\n      mermaidOptions\n    } = options;\n    if (katex) {\n      this.renderKatex(element, {\n        ...this.DEFAULT_KATEX_OPTIONS,\n        ...katexOptions\n      });\n    }\n    if (mermaid) {\n      this.renderMermaid(element, {\n        ...this.DEFAULT_MERMAID_OPTIONS,\n        ...this.mermaidOptions,\n        ...mermaidOptions\n      });\n    }\n    if (clipboard) {\n      this.renderClipboard(element, viewContainerRef, {\n        ...this.DEFAULT_CLIPBOARD_OPTIONS,\n        ...this.clipboardOptions,\n        ...clipboardOptions\n      });\n    }\n    this.highlight(element);\n  }\n  reload() {\n    this._reload$.next();\n  }\n  getSource(src) {\n    if (!this.http) {\n      throw new Error(errorSrcWithoutHttpClient);\n    }\n    return this.http.get(src, {\n      responseType: 'text'\n    }).pipe(map(markdown => this.handleExtension(src, markdown)));\n  }\n  highlight(element) {\n    if (!isPlatformBrowser(this.platform)) {\n      return;\n    }\n    if (typeof Prism === 'undefined' || typeof Prism.highlightAllUnder === 'undefined') {\n      return;\n    }\n    if (!element) {\n      element = document;\n    }\n    const noLanguageElements = element.querySelectorAll('pre code:not([class*=\"language-\"])');\n    Array.prototype.forEach.call(noLanguageElements, x => x.classList.add('language-none'));\n    Prism.highlightAllUnder(element);\n  }\n  decodeHtml(html) {\n    if (!isPlatformBrowser(this.platform)) {\n      return html;\n    }\n    const textarea = document.createElement('textarea');\n    textarea.innerHTML = html;\n    return textarea.value;\n  }\n  extendsRendererForExtensions(renderer) {\n    const extendedRenderer = renderer;\n    if (extendedRenderer.ɵNgxMarkdownRendererExtendedForExtensions === true) {\n      return renderer;\n    }\n    if (this.extensions?.length > 0) {\n      marked.use(...this.extensions);\n    }\n    extendedRenderer.ɵNgxMarkdownRendererExtendedForExtensions = true;\n    return renderer;\n  }\n  extendsRendererForMermaid(renderer) {\n    const extendedRenderer = renderer;\n    if (extendedRenderer.ɵNgxMarkdownRendererExtendedForMermaid === true) {\n      return renderer;\n    }\n    const defaultCode = renderer.code;\n    renderer.code = token => {\n      return token.lang === 'mermaid' ? `<div class=\"mermaid\">${token.text}</div>` : defaultCode(token);\n    };\n    extendedRenderer.ɵNgxMarkdownRendererExtendedForMermaid = true;\n    return renderer;\n  }\n  handleExtension(src, markdown) {\n    const urlProtocolIndex = src.lastIndexOf('://');\n    const urlWithoutProtocol = urlProtocolIndex > -1 ? src.substring(urlProtocolIndex + 4) : src;\n    const lastSlashIndex = urlWithoutProtocol.lastIndexOf('/');\n    const lastUrlSegment = lastSlashIndex > -1 ? urlWithoutProtocol.substring(lastSlashIndex + 1).split('?')[0] : '';\n    const lastDotIndex = lastUrlSegment.lastIndexOf('.');\n    const extension = lastDotIndex > -1 ? lastUrlSegment.substring(lastDotIndex + 1) : '';\n    return !!extension && extension !== 'md' ? '```' + extension + '\\n' + markdown + '\\n```' : markdown;\n  }\n  parseMarked(html, markedOptions, inline = false) {\n    if (markedOptions.renderer) {\n      // clone renderer and remove extended flags otherwise\n      // marked throws an error thinking it is a renderer prop\n      const renderer = {\n        ...markedOptions.renderer\n      };\n      delete renderer.ɵNgxMarkdownRendererExtendedForExtensions;\n      delete renderer.ɵNgxMarkdownRendererExtendedForMermaid;\n      // remove renderer from markedOptions because if renderer is\n      // passed to marked.parse method, it will ignore all extensions\n      delete markedOptions.renderer;\n      marked.use({\n        renderer\n      });\n    }\n    return inline ? marked.parseInline(html, markedOptions) : marked.parse(html, markedOptions);\n  }\n  parseEmoji(html) {\n    if (!isPlatformBrowser(this.platform)) {\n      return html;\n    }\n    if (typeof joypixels === 'undefined' || typeof joypixels.shortnameToUnicode === 'undefined') {\n      throw new Error(errorJoyPixelsNotLoaded);\n    }\n    return joypixels.shortnameToUnicode(html);\n  }\n  renderKatex(element, options) {\n    if (!isPlatformBrowser(this.platform)) {\n      return;\n    }\n    if (typeof katex === 'undefined' || typeof renderMathInElement === 'undefined') {\n      throw new Error(errorKatexNotLoaded);\n    }\n    renderMathInElement(element, options);\n  }\n  renderClipboard(element, viewContainerRef, options) {\n    if (!isPlatformBrowser(this.platform)) {\n      return;\n    }\n    if (typeof ClipboardJS === 'undefined') {\n      throw new Error(errorClipboardNotLoaded);\n    }\n    if (!viewContainerRef) {\n      throw new Error(errorClipboardViewContainerRequired);\n    }\n    const {\n      buttonComponent,\n      buttonTemplate\n    } = options;\n    // target every <pre> elements\n    const preElements = element.querySelectorAll('pre');\n    for (let i = 0; i < preElements.length; i++) {\n      const preElement = preElements.item(i);\n      // create <pre> wrapper element\n      const preWrapperElement = document.createElement('div');\n      preWrapperElement.style.position = 'relative';\n      preElement.parentNode.insertBefore(preWrapperElement, preElement);\n      preWrapperElement.appendChild(preElement);\n      // create toolbar element\n      const toolbarWrapperElement = document.createElement('div');\n      toolbarWrapperElement.classList.add('markdown-clipboard-toolbar');\n      toolbarWrapperElement.style.position = 'absolute';\n      toolbarWrapperElement.style.top = '.5em';\n      toolbarWrapperElement.style.right = '.5em';\n      toolbarWrapperElement.style.zIndex = '1';\n      preWrapperElement.insertAdjacentElement('beforeend', toolbarWrapperElement);\n      // register listener to show/hide toolbar\n      preWrapperElement.onmouseenter = () => toolbarWrapperElement.classList.add('hover');\n      preWrapperElement.onmouseleave = () => toolbarWrapperElement.classList.remove('hover');\n      // declare embeddedViewRef holding variable\n      let embeddedViewRef;\n      // use provided component via input property\n      // or provided via ClipboardOptions provider\n      if (buttonComponent) {\n        const componentRef = viewContainerRef.createComponent(buttonComponent);\n        embeddedViewRef = componentRef.hostView;\n        componentRef.changeDetectorRef.markForCheck();\n      }\n      // use provided template via input property\n      else if (buttonTemplate) {\n        embeddedViewRef = viewContainerRef.createEmbeddedView(buttonTemplate);\n      }\n      // use default component\n      else {\n        const componentRef = viewContainerRef.createComponent(ClipboardButtonComponent);\n        embeddedViewRef = componentRef.hostView;\n        componentRef.changeDetectorRef.markForCheck();\n      }\n      // declare clipboard instance variable\n      let clipboardInstance;\n      // attach clipboard.js to root node\n      embeddedViewRef.rootNodes.forEach(node => {\n        toolbarWrapperElement.appendChild(node);\n        clipboardInstance = new ClipboardJS(node, {\n          text: () => preElement.innerText\n        });\n      });\n      // destroy clipboard instance when view is destroyed\n      embeddedViewRef.onDestroy(() => clipboardInstance.destroy());\n    }\n  }\n  renderMermaid(element, options = this.DEFAULT_MERMAID_OPTIONS) {\n    if (!isPlatformBrowser(this.platform)) {\n      return;\n    }\n    if (typeof mermaid === 'undefined' || typeof mermaid.initialize === 'undefined') {\n      throw new Error(errorMermaidNotLoaded);\n    }\n    const mermaidElements = element.querySelectorAll('.mermaid');\n    if (mermaidElements.length === 0) {\n      return;\n    }\n    mermaid.initialize(options);\n    mermaid.run({\n      nodes: mermaidElements\n    });\n  }\n  trimIndentation(markdown) {\n    if (!markdown) {\n      return '';\n    }\n    let indentStart;\n    return markdown.split('\\n').map(line => {\n      let lineIdentStart = indentStart;\n      if (line.length > 0) {\n        lineIdentStart = isNaN(lineIdentStart) ? line.search(/\\S|$/) : Math.min(line.search(/\\S|$/), lineIdentStart);\n      }\n      if (isNaN(indentStart)) {\n        indentStart = lineIdentStart;\n      }\n      return lineIdentStart ? line.substring(lineIdentStart) : line;\n    }).join('\\n');\n  }\n  static {\n    this.ɵfac = function MarkdownService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MarkdownService)(i0.ɵɵinject(CLIPBOARD_OPTIONS, 8), i0.ɵɵinject(MARKED_EXTENSIONS, 8), i0.ɵɵinject(MARKED_OPTIONS, 8), i0.ɵɵinject(MERMAID_OPTIONS, 8), i0.ɵɵinject(PLATFORM_ID), i0.ɵɵinject(SECURITY_CONTEXT), i0.ɵɵinject(i1.HttpClient, 8), i0.ɵɵinject(i2.DomSanitizer));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: MarkdownService,\n      factory: MarkdownService.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MarkdownService, [{\n    type: Injectable\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [CLIPBOARD_OPTIONS]\n    }, {\n      type: Optional\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [MARKED_EXTENSIONS]\n    }, {\n      type: Optional\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [MARKED_OPTIONS]\n    }, {\n      type: Optional\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [MERMAID_OPTIONS]\n    }, {\n      type: Optional\n    }]\n  }, {\n    type: Object,\n    decorators: [{\n      type: Inject,\n      args: [PLATFORM_ID]\n    }]\n  }, {\n    type: i0.SecurityContext,\n    decorators: [{\n      type: Inject,\n      args: [SECURITY_CONTEXT]\n    }]\n  }, {\n    type: i1.HttpClient,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: i2.DomSanitizer\n  }], null);\n})();\nclass MarkdownComponent {\n  get disableSanitizer() {\n    return this._disableSanitizer;\n  }\n  set disableSanitizer(value) {\n    this._disableSanitizer = this.coerceBooleanProperty(value);\n  }\n  get inline() {\n    return this._inline;\n  }\n  set inline(value) {\n    this._inline = this.coerceBooleanProperty(value);\n  }\n  // Plugin - clipboard\n  get clipboard() {\n    return this._clipboard;\n  }\n  set clipboard(value) {\n    this._clipboard = this.coerceBooleanProperty(value);\n  }\n  // Plugin - emoji\n  get emoji() {\n    return this._emoji;\n  }\n  set emoji(value) {\n    this._emoji = this.coerceBooleanProperty(value);\n  }\n  // Plugin - katex\n  get katex() {\n    return this._katex;\n  }\n  set katex(value) {\n    this._katex = this.coerceBooleanProperty(value);\n  }\n  // Plugin - mermaid\n  get mermaid() {\n    return this._mermaid;\n  }\n  set mermaid(value) {\n    this._mermaid = this.coerceBooleanProperty(value);\n  }\n  // Plugin - lineHighlight\n  get lineHighlight() {\n    return this._lineHighlight;\n  }\n  set lineHighlight(value) {\n    this._lineHighlight = this.coerceBooleanProperty(value);\n  }\n  // Plugin - lineNumbers\n  get lineNumbers() {\n    return this._lineNumbers;\n  }\n  set lineNumbers(value) {\n    this._lineNumbers = this.coerceBooleanProperty(value);\n  }\n  // Plugin - commandLine\n  get commandLine() {\n    return this._commandLine;\n  }\n  set commandLine(value) {\n    this._commandLine = this.coerceBooleanProperty(value);\n  }\n  constructor(element, markdownService, viewContainerRef) {\n    this.element = element;\n    this.markdownService = markdownService;\n    this.viewContainerRef = viewContainerRef;\n    // Event emitters\n    this.error = new EventEmitter();\n    this.load = new EventEmitter();\n    this.ready = new EventEmitter();\n    this._clipboard = false;\n    this._commandLine = false;\n    this._disableSanitizer = false;\n    this._emoji = false;\n    this._inline = false;\n    this._katex = false;\n    this._lineHighlight = false;\n    this._lineNumbers = false;\n    this._mermaid = false;\n    this.destroyed$ = new Subject();\n  }\n  ngOnChanges() {\n    this.loadContent();\n  }\n  loadContent() {\n    if (this.data != null) {\n      this.handleData();\n      return;\n    }\n    if (this.src != null) {\n      this.handleSrc();\n      return;\n    }\n  }\n  ngAfterViewInit() {\n    if (!this.data && !this.src) {\n      this.handleTransclusion();\n    }\n    this.markdownService.reload$.pipe(takeUntil(this.destroyed$)).subscribe(() => this.loadContent());\n  }\n  ngOnDestroy() {\n    this.destroyed$.next();\n    this.destroyed$.complete();\n  }\n  async render(markdown, decodeHtml = false) {\n    const parsedOptions = {\n      decodeHtml,\n      inline: this.inline,\n      emoji: this.emoji,\n      mermaid: this.mermaid,\n      disableSanitizer: this.disableSanitizer\n    };\n    const renderOptions = {\n      clipboard: this.clipboard,\n      clipboardOptions: this.getClipboardOptions(),\n      katex: this.katex,\n      katexOptions: this.katexOptions,\n      mermaid: this.mermaid,\n      mermaidOptions: this.mermaidOptions\n    };\n    const parsed = await this.markdownService.parse(markdown, parsedOptions);\n    this.element.nativeElement.innerHTML = parsed;\n    this.handlePlugins();\n    this.markdownService.render(this.element.nativeElement, renderOptions, this.viewContainerRef);\n    this.ready.emit();\n  }\n  coerceBooleanProperty(value) {\n    return value != null && `${String(value)}` !== 'false';\n  }\n  getClipboardOptions() {\n    if (this.clipboardButtonComponent || this.clipboardButtonTemplate) {\n      return {\n        buttonComponent: this.clipboardButtonComponent,\n        buttonTemplate: this.clipboardButtonTemplate\n      };\n    }\n    return undefined;\n  }\n  handleData() {\n    this.render(this.data);\n  }\n  handleSrc() {\n    this.markdownService.getSource(this.src).subscribe({\n      next: markdown => {\n        this.render(markdown).then(() => {\n          this.load.emit(markdown);\n        });\n      },\n      error: error => this.error.emit(error)\n    });\n  }\n  handleTransclusion() {\n    this.render(this.element.nativeElement.innerHTML, true);\n  }\n  handlePlugins() {\n    if (this.commandLine) {\n      this.setPluginClass(this.element.nativeElement, PrismPlugin.CommandLine);\n      this.setPluginOptions(this.element.nativeElement, {\n        dataFilterOutput: this.filterOutput,\n        dataHost: this.host,\n        dataPrompt: this.prompt,\n        dataOutput: this.output,\n        dataUser: this.user\n      });\n    }\n    if (this.lineHighlight) {\n      this.setPluginOptions(this.element.nativeElement, {\n        dataLine: this.line,\n        dataLineOffset: this.lineOffset\n      });\n    }\n    if (this.lineNumbers) {\n      this.setPluginClass(this.element.nativeElement, PrismPlugin.LineNumbers);\n      this.setPluginOptions(this.element.nativeElement, {\n        dataStart: this.start\n      });\n    }\n  }\n  setPluginClass(element, plugin) {\n    const preElements = element.querySelectorAll('pre');\n    for (let i = 0; i < preElements.length; i++) {\n      const classes = plugin instanceof Array ? plugin : [plugin];\n      preElements.item(i).classList.add(...classes);\n    }\n  }\n  setPluginOptions(element, options) {\n    const preElements = element.querySelectorAll('pre');\n    for (let i = 0; i < preElements.length; i++) {\n      Object.keys(options).forEach(option => {\n        const attributeValue = options[option];\n        if (attributeValue) {\n          const attributeName = this.toLispCase(option);\n          preElements.item(i).setAttribute(attributeName, attributeValue.toString());\n        }\n      });\n    }\n  }\n  toLispCase(value) {\n    const upperChars = value.match(/([A-Z])/g);\n    if (!upperChars) {\n      return value;\n    }\n    let str = value.toString();\n    for (let i = 0, n = upperChars.length; i < n; i++) {\n      str = str.replace(new RegExp(upperChars[i]), '-' + upperChars[i].toLowerCase());\n    }\n    if (str.slice(0, 1) === '-') {\n      str = str.slice(1);\n    }\n    return str;\n  }\n  static {\n    this.ɵfac = function MarkdownComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MarkdownComponent)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(MarkdownService), i0.ɵɵdirectiveInject(i0.ViewContainerRef));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MarkdownComponent,\n      selectors: [[\"markdown\"], [\"\", \"markdown\", \"\"]],\n      inputs: {\n        data: \"data\",\n        src: \"src\",\n        disableSanitizer: \"disableSanitizer\",\n        inline: \"inline\",\n        clipboard: \"clipboard\",\n        clipboardButtonComponent: \"clipboardButtonComponent\",\n        clipboardButtonTemplate: \"clipboardButtonTemplate\",\n        emoji: \"emoji\",\n        katex: \"katex\",\n        katexOptions: \"katexOptions\",\n        mermaid: \"mermaid\",\n        mermaidOptions: \"mermaidOptions\",\n        lineHighlight: \"lineHighlight\",\n        line: \"line\",\n        lineOffset: \"lineOffset\",\n        lineNumbers: \"lineNumbers\",\n        start: \"start\",\n        commandLine: \"commandLine\",\n        filterOutput: \"filterOutput\",\n        host: \"host\",\n        prompt: \"prompt\",\n        output: \"output\",\n        user: \"user\"\n      },\n      outputs: {\n        error: \"error\",\n        load: \"load\",\n        ready: \"ready\"\n      },\n      features: [i0.ɵɵNgOnChangesFeature],\n      ngContentSelectors: _c0,\n      decls: 1,\n      vars: 0,\n      template: function MarkdownComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵprojection(0);\n        }\n      },\n      encapsulation: 2\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MarkdownComponent, [{\n    type: Component,\n    args: [{\n      // eslint-disable-next-line @angular-eslint/component-selector\n      selector: 'markdown, [markdown]',\n      template: '<ng-content></ng-content>'\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: MarkdownService\n  }, {\n    type: i0.ViewContainerRef\n  }], {\n    data: [{\n      type: Input\n    }],\n    src: [{\n      type: Input\n    }],\n    disableSanitizer: [{\n      type: Input\n    }],\n    inline: [{\n      type: Input\n    }],\n    clipboard: [{\n      type: Input\n    }],\n    clipboardButtonComponent: [{\n      type: Input\n    }],\n    clipboardButtonTemplate: [{\n      type: Input\n    }],\n    emoji: [{\n      type: Input\n    }],\n    katex: [{\n      type: Input\n    }],\n    katexOptions: [{\n      type: Input\n    }],\n    mermaid: [{\n      type: Input\n    }],\n    mermaidOptions: [{\n      type: Input\n    }],\n    lineHighlight: [{\n      type: Input\n    }],\n    line: [{\n      type: Input\n    }],\n    lineOffset: [{\n      type: Input\n    }],\n    lineNumbers: [{\n      type: Input\n    }],\n    start: [{\n      type: Input\n    }],\n    commandLine: [{\n      type: Input\n    }],\n    filterOutput: [{\n      type: Input\n    }],\n    host: [{\n      type: Input\n    }],\n    prompt: [{\n      type: Input\n    }],\n    output: [{\n      type: Input\n    }],\n    user: [{\n      type: Input\n    }],\n    error: [{\n      type: Output\n    }],\n    load: [{\n      type: Output\n    }],\n    ready: [{\n      type: Output\n    }]\n  });\n})();\nclass MarkdownPipe {\n  constructor(domSanitizer, elementRef, markdownService, viewContainerRef, zone) {\n    this.domSanitizer = domSanitizer;\n    this.elementRef = elementRef;\n    this.markdownService = markdownService;\n    this.viewContainerRef = viewContainerRef;\n    this.zone = zone;\n  }\n  async transform(value, options) {\n    if (value == null) {\n      return '';\n    }\n    if (typeof value !== 'string') {\n      console.error(`MarkdownPipe has been invoked with an invalid value type [${typeof value}]`);\n      return value;\n    }\n    const markdown = await this.markdownService.parse(value, options);\n    this.zone.onStable.pipe(first()).subscribe(() => this.markdownService.render(this.elementRef.nativeElement, options, this.viewContainerRef));\n    return this.domSanitizer.bypassSecurityTrustHtml(markdown);\n  }\n  static {\n    this.ɵfac = function MarkdownPipe_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MarkdownPipe)(i0.ɵɵdirectiveInject(i2.DomSanitizer, 16), i0.ɵɵdirectiveInject(i0.ElementRef, 16), i0.ɵɵdirectiveInject(MarkdownService, 16), i0.ɵɵdirectiveInject(i0.ViewContainerRef, 16), i0.ɵɵdirectiveInject(i0.NgZone, 16));\n    };\n  }\n  static {\n    this.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n      name: \"markdown\",\n      type: MarkdownPipe,\n      pure: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MarkdownPipe, [{\n    type: Pipe,\n    args: [{\n      name: 'markdown'\n    }]\n  }], () => [{\n    type: i2.DomSanitizer\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: MarkdownService\n  }, {\n    type: i0.ViewContainerRef\n  }, {\n    type: i0.NgZone\n  }], null);\n})();\nfunction provideMarkdown(markdownModuleConfig) {\n  return [MarkdownService, markdownModuleConfig?.loader ?? [], markdownModuleConfig?.clipboardOptions ?? [], markdownModuleConfig?.markedOptions ?? [], markdownModuleConfig?.mermaidOptions ?? [], markdownModuleConfig?.markedExtensions ?? [], {\n    provide: SECURITY_CONTEXT,\n    useValue: markdownModuleConfig?.sanitize ?? SecurityContext.HTML\n  }];\n}\n;\n;\nconst sharedDeclarations = [ClipboardButtonComponent, LanguagePipe, MarkdownComponent, MarkdownPipe];\nclass MarkdownModule {\n  static forRoot(markdownModuleConfig) {\n    return {\n      ngModule: MarkdownModule,\n      providers: [provideMarkdown(markdownModuleConfig)]\n    };\n  }\n  static forChild() {\n    return {\n      ngModule: MarkdownModule\n    };\n  }\n  static {\n    this.ɵfac = function MarkdownModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MarkdownModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MarkdownModule,\n      imports: [ClipboardButtonComponent, LanguagePipe, MarkdownComponent, MarkdownPipe],\n      exports: [ClipboardButtonComponent, LanguagePipe, MarkdownComponent, MarkdownPipe]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MarkdownModule, [{\n    type: NgModule,\n    args: [{\n      imports: sharedDeclarations,\n      exports: sharedDeclarations\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CLIPBOARD_OPTIONS, ClipboardButtonComponent, ExtendedRenderer, KatexSpecificOptions, LanguagePipe, MARKED_EXTENSIONS, MARKED_OPTIONS, MERMAID_OPTIONS, MarkdownComponent, MarkdownModule, MarkdownPipe, MarkdownService, PrismPlugin, SECURITY_CONTEXT, errorClipboardNotLoaded, errorClipboardViewContainerRequired, errorJoyPixelsNotLoaded, errorKatexNotLoaded, errorMermaidNotLoaded, errorSrcWithoutHttpClient, provideMarkdown };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsLA,SAAS,SAAS,QAAQA,UAAS;AAC/B,SAAO,cAAc,eACjB,aACA,2BAA2B,UAAU,6JACmE;AAC5G,QAAM,kBAAkB,CAACA,UAAS;AAClC,MAAI,aAAa,mBAAmB,CAACA,UAAS,UAAU;AACpD,6BAAyB,QAAQ;AAAA,EACrC;AACA,QAAM,aAAa,kBACZA,UAAS,UAAU,IAAI,UAAU,KAAK,OAAO,UAAU,IACxD;AACN,QAAM,QAAQ,kBAAkBA,UAAS,KAAK;AAG9C,MAAI;AACJ,MAAIA,UAAS,aAAa;AAEtB,YAAQ,OAAO;AAAA,MAAE,MAAM;AAAA;AAAA,IAA0B,GAAG,EAAE,MAAM,CAAC;AAAA,EACjE,OACK;AAED,YAAQ,OAAO,EAAE,MAAM,GAAyB,OAAOA,UAAS,aAAa,GAAG,EAAE,MAAM,CAAC;AAAA,EAC7F;AACA,MAAI;AAOJ,QAAM,MAAM,OAAO,UAAU;AAAA,IACzB,MAAM,CAAC,UAAU,MAAM,IAAI,EAAE,MAAM,GAAyB,MAAM,CAAC;AAAA,IACnE,OAAO,CAAC,UAAU;AACd,YAAM,IAAI,EAAE,MAAM,GAAyB,MAAM,CAAC;AAClD,4BAAsB;AAAA,IAC1B;AAAA,IACA,UAAU,MAAM;AACZ,4BAAsB;AAAA,IAC1B;AAAA;AAAA;AAAA,EAGJ,CAAC;AACD,MAAIA,UAAS,eAAe,MAAM,EAAE,SAAS,GAA2B;AACpE,UAAM,IAAI,aAAa,MAA6D,OAAO,cAAc,eAAe,cACpH,qFAAqF;AAAA,EAC7F;AAEA,wBAAsB,YAAY,UAAU,IAAI,YAAY,KAAK,GAAG,CAAC;AAGrE,SAAO,SAAS,MAAM;AAClB,UAAM,UAAU,MAAM;AACtB,YAAQ,QAAQ,MAAM;AAAA,MAClB,KAAK;AACD,eAAO,QAAQ;AAAA,MACnB,KAAK;AACD,cAAM,QAAQ;AAAA,MAClB,KAAK;AAED,cAAM,IAAI,aAAa,MAA6D,OAAO,cAAc,eAAe,cACpH,qFAAqF;AAAA,IACjG;AAAA,EACJ,GAAG,EAAE,OAAOA,UAAS,MAAM,CAAC;AAChC;AACA,SAAS,kBAAkB,eAAe,OAAO,IAAI;AACjD,SAAO,CAAC,GAAG,MAAM,EAAE,SAAS,KAA2B,EAAE,SAAS,KAA2B,aAAa,EAAE,OAAO,EAAE,KAAK;AAC9H;;;AC5OA,SAAS,eAAe;AACtB,SAAO;AAAA,IACL,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,KAAK;AAAA,IACL,OAAO;AAAA,IACP,UAAU;AAAA,IACV,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,YAAY;AAAA,EACd;AACF;AACA,IAAI,YAAY,aAAa;AAC7B,SAAS,eAAe,aAAa;AACnC,cAAY;AACd;AAGA,IAAI,WAAW,EAAE,MAAM,MAAM,KAAK;AAClC,SAAS,KAAK,OAAO,MAAM,IAAI;AAC7B,MAAI,SAAS,OAAO,UAAU,WAAW,QAAQ,MAAM;AACvD,QAAM,MAAM;AAAA,IACV,SAAS,CAAC,MAAM,QAAQ;AACtB,UAAI,YAAY,OAAO,QAAQ,WAAW,MAAM,IAAI;AACpD,kBAAY,UAAU,QAAQ,MAAM,OAAO,IAAI;AAC/C,eAAS,OAAO,QAAQ,MAAM,SAAS;AACvC,aAAO;AAAA,IACT;AAAA,IACA,UAAU,MAAM;AACd,aAAO,IAAI,OAAO,QAAQ,GAAG;AAAA,IAC/B;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAI,QAAQ;AAAA,EACV,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB,gBAAgB;AAAA,EAChB,YAAY;AAAA,EACZ,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,cAAc;AAAA,EACd,mBAAmB;AAAA,EACnB,eAAe;AAAA,EACf,qBAAqB;AAAA,EACrB,WAAW;AAAA,EACX,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,iBAAiB;AAAA,EACjB,oBAAoB;AAAA,EACpB,YAAY;AAAA,EACZ,iBAAiB;AAAA,EACjB,SAAS;AAAA,EACT,cAAc;AAAA,EACd,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,kBAAkB;AAAA,EAClB,gBAAgB;AAAA,EAChB,WAAW;AAAA,EACX,SAAS;AAAA,EACT,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,YAAY;AAAA,EACZ,eAAe;AAAA,EACf,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,cAAc;AAAA,EACd,OAAO;AAAA,EACP,eAAe;AAAA,EACf,UAAU;AAAA,EACV,WAAW;AAAA,EACX,WAAW;AAAA,EACX,gBAAgB;AAAA,EAChB,WAAW;AAAA,EACX,eAAe;AAAA,EACf,eAAe;AAAA,EACf,eAAe,CAAC,SAAS,IAAI,OAAO,WAAW,IAAI,8BAA8B;AAAA,EACjF,iBAAiB,CAAC,WAAW,IAAI,OAAO,QAAQ,KAAK,IAAI,GAAG,SAAS,CAAC,CAAC,oDAAoD;AAAA,EAC3H,SAAS,CAAC,WAAW,IAAI,OAAO,QAAQ,KAAK,IAAI,GAAG,SAAS,CAAC,CAAC,oDAAoD;AAAA,EACnH,kBAAkB,CAAC,WAAW,IAAI,OAAO,QAAQ,KAAK,IAAI,GAAG,SAAS,CAAC,CAAC,iBAAiB;AAAA,EACzF,mBAAmB,CAAC,WAAW,IAAI,OAAO,QAAQ,KAAK,IAAI,GAAG,SAAS,CAAC,CAAC,IAAI;AAAA,EAC7E,gBAAgB,CAAC,WAAW,IAAI,OAAO,QAAQ,KAAK,IAAI,GAAG,SAAS,CAAC,CAAC,sBAAsB,GAAG;AACjG;AACA,IAAI,UAAU;AACd,IAAI,YAAY;AAChB,IAAI,SAAS;AACb,IAAI,KAAK;AACT,IAAI,UAAU;AACd,IAAI,SAAS;AACb,IAAI,eAAe;AACnB,IAAI,WAAW,KAAK,YAAY,EAAE,QAAQ,SAAS,MAAM,EAAE,QAAQ,cAAc,mBAAmB,EAAE,QAAQ,WAAW,uBAAuB,EAAE,QAAQ,eAAe,SAAS,EAAE,QAAQ,YAAY,cAAc,EAAE,QAAQ,SAAS,mBAAmB,EAAE,QAAQ,YAAY,EAAE,EAAE,SAAS;AAC/R,IAAI,cAAc,KAAK,YAAY,EAAE,QAAQ,SAAS,MAAM,EAAE,QAAQ,cAAc,mBAAmB,EAAE,QAAQ,WAAW,uBAAuB,EAAE,QAAQ,eAAe,SAAS,EAAE,QAAQ,YAAY,cAAc,EAAE,QAAQ,SAAS,mBAAmB,EAAE,QAAQ,UAAU,mCAAmC,EAAE,SAAS;AACjU,IAAI,aAAa;AACjB,IAAI,YAAY;AAChB,IAAI,cAAc;AAClB,IAAI,MAAM,KAAK,6GAA6G,EAAE,QAAQ,SAAS,WAAW,EAAE,QAAQ,SAAS,8DAA8D,EAAE,SAAS;AACtP,IAAI,OAAO,KAAK,sCAAsC,EAAE,QAAQ,SAAS,MAAM,EAAE,SAAS;AAC1F,IAAI,OAAO;AACX,IAAI,WAAW;AACf,IAAI,OAAO;AAAA,EACT;AAAA,EACA;AACF,EAAE,QAAQ,WAAW,QAAQ,EAAE,QAAQ,OAAO,IAAI,EAAE,QAAQ,aAAa,0EAA0E,EAAE,SAAS;AAC9J,IAAI,YAAY,KAAK,UAAU,EAAE,QAAQ,MAAM,EAAE,EAAE,QAAQ,WAAW,uBAAuB,EAAE,QAAQ,aAAa,EAAE,EAAE,QAAQ,UAAU,EAAE,EAAE,QAAQ,cAAc,SAAS,EAAE,QAAQ,UAAU,gDAAgD,EAAE,QAAQ,QAAQ,wBAAwB,EAAE,QAAQ,QAAQ,6DAA6D,EAAE,QAAQ,OAAO,IAAI,EAAE,SAAS;AAC1Y,IAAI,aAAa,KAAK,yCAAyC,EAAE,QAAQ,aAAa,SAAS,EAAE,SAAS;AAC1G,IAAI,cAAc;AAAA,EAChB;AAAA,EACA,MAAM;AAAA,EACN;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,OAAO;AAAA,EACP,MAAM;AACR;AACA,IAAI,WAAW;AAAA,EACb;AACF,EAAE,QAAQ,MAAM,EAAE,EAAE,QAAQ,WAAW,uBAAuB,EAAE,QAAQ,cAAc,SAAS,EAAE,QAAQ,QAAQ,wBAAwB,EAAE,QAAQ,UAAU,gDAAgD,EAAE,QAAQ,QAAQ,wBAAwB,EAAE,QAAQ,QAAQ,6DAA6D,EAAE,QAAQ,OAAO,IAAI,EAAE,SAAS;AACtW,IAAI,WAAW,iCACV,cADU;AAAA,EAEb,UAAU;AAAA,EACV,OAAO;AAAA,EACP,WAAW,KAAK,UAAU,EAAE,QAAQ,MAAM,EAAE,EAAE,QAAQ,WAAW,uBAAuB,EAAE,QAAQ,aAAa,EAAE,EAAE,QAAQ,SAAS,QAAQ,EAAE,QAAQ,cAAc,SAAS,EAAE,QAAQ,UAAU,gDAAgD,EAAE,QAAQ,QAAQ,wBAAwB,EAAE,QAAQ,QAAQ,6DAA6D,EAAE,QAAQ,OAAO,IAAI,EAAE,SAAS;AAC5Y;AACA,IAAI,gBAAgB,iCACf,cADe;AAAA,EAElB,MAAM;AAAA,IACJ;AAAA,EACF,EAAE,QAAQ,WAAW,QAAQ,EAAE,QAAQ,QAAQ,mKAAmK,EAAE,SAAS;AAAA,EAC7N,KAAK;AAAA,EACL,SAAS;AAAA,EACT,QAAQ;AAAA;AAAA,EAER,UAAU;AAAA,EACV,WAAW,KAAK,UAAU,EAAE,QAAQ,MAAM,EAAE,EAAE,QAAQ,WAAW,iBAAiB,EAAE,QAAQ,YAAY,QAAQ,EAAE,QAAQ,UAAU,EAAE,EAAE,QAAQ,cAAc,SAAS,EAAE,QAAQ,WAAW,EAAE,EAAE,QAAQ,SAAS,EAAE,EAAE,QAAQ,SAAS,EAAE,EAAE,QAAQ,QAAQ,EAAE,EAAE,SAAS;AACzQ;AACA,IAAI,SAAS;AACb,IAAI,aAAa;AACjB,IAAI,KAAK;AACT,IAAI,aAAa;AACjB,IAAI,eAAe;AACnB,IAAI,sBAAsB;AAC1B,IAAI,yBAAyB;AAC7B,IAAI,cAAc,KAAK,yBAAyB,GAAG,EAAE,QAAQ,eAAe,mBAAmB,EAAE,SAAS;AAC1G,IAAI,0BAA0B;AAC9B,IAAI,iCAAiC;AACrC,IAAI,oCAAoC;AACxC,IAAI,YAAY;AAChB,IAAI,qBAAqB;AACzB,IAAI,iBAAiB,KAAK,oBAAoB,GAAG,EAAE,QAAQ,UAAU,YAAY,EAAE,SAAS;AAC5F,IAAI,oBAAoB,KAAK,oBAAoB,GAAG,EAAE,QAAQ,UAAU,uBAAuB,EAAE,SAAS;AAC1G,IAAI,wBAAwB;AAC5B,IAAI,oBAAoB,KAAK,uBAAuB,IAAI,EAAE,QAAQ,kBAAkB,sBAAsB,EAAE,QAAQ,eAAe,mBAAmB,EAAE,QAAQ,UAAU,YAAY,EAAE,SAAS;AACjM,IAAI,uBAAuB,KAAK,uBAAuB,IAAI,EAAE,QAAQ,kBAAkB,iCAAiC,EAAE,QAAQ,eAAe,8BAA8B,EAAE,QAAQ,UAAU,uBAAuB,EAAE,SAAS;AACrO,IAAI,oBAAoB;AAAA,EACtB;AAAA,EACA;AACF,EAAE,QAAQ,kBAAkB,sBAAsB,EAAE,QAAQ,eAAe,mBAAmB,EAAE,QAAQ,UAAU,YAAY,EAAE,SAAS;AACzI,IAAI,iBAAiB,KAAK,aAAa,IAAI,EAAE,QAAQ,UAAU,YAAY,EAAE,SAAS;AACtF,IAAI,WAAW,KAAK,qCAAqC,EAAE,QAAQ,UAAU,8BAA8B,EAAE,QAAQ,SAAS,8IAA8I,EAAE,SAAS;AACvR,IAAI,iBAAiB,KAAK,QAAQ,EAAE,QAAQ,aAAa,KAAK,EAAE,SAAS;AACzE,IAAI,MAAM;AAAA,EACR;AACF,EAAE,QAAQ,WAAW,cAAc,EAAE,QAAQ,aAAa,6EAA6E,EAAE,SAAS;AAClJ,IAAI,eAAe;AACnB,IAAI,OAAO,KAAK,mEAAmE,EAAE,QAAQ,SAAS,YAAY,EAAE,QAAQ,QAAQ,yCAAyC,EAAE,QAAQ,SAAS,6DAA6D,EAAE,SAAS;AACxQ,IAAI,UAAU,KAAK,yBAAyB,EAAE,QAAQ,SAAS,YAAY,EAAE,QAAQ,OAAO,WAAW,EAAE,SAAS;AAClH,IAAI,SAAS,KAAK,uBAAuB,EAAE,QAAQ,OAAO,WAAW,EAAE,SAAS;AAChF,IAAI,gBAAgB,KAAK,yBAAyB,GAAG,EAAE,QAAQ,WAAW,OAAO,EAAE,QAAQ,UAAU,MAAM,EAAE,SAAS;AACtH,IAAI,eAAe;AAAA,EACjB,YAAY;AAAA;AAAA,EAEZ;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,MAAM;AAAA,EACN,KAAK;AAAA,EACL;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,MAAM;AAAA,EACN,KAAK;AACP;AACA,IAAI,iBAAiB,iCAChB,eADgB;AAAA,EAEnB,MAAM,KAAK,yBAAyB,EAAE,QAAQ,SAAS,YAAY,EAAE,SAAS;AAAA,EAC9E,SAAS,KAAK,+BAA+B,EAAE,QAAQ,SAAS,YAAY,EAAE,SAAS;AACzF;AACA,IAAI,YAAY,iCACX,eADW;AAAA,EAEd,mBAAmB;AAAA,EACnB,gBAAgB;AAAA,EAChB,KAAK,KAAK,oEAAoE,GAAG,EAAE,QAAQ,SAAS,2EAA2E,EAAE,SAAS;AAAA,EAC1L,YAAY;AAAA,EACZ,KAAK;AAAA,EACL,MAAM;AACR;AACA,IAAI,eAAe,iCACd,YADc;AAAA,EAEjB,IAAI,KAAK,EAAE,EAAE,QAAQ,QAAQ,GAAG,EAAE,SAAS;AAAA,EAC3C,MAAM,KAAK,UAAU,IAAI,EAAE,QAAQ,QAAQ,eAAe,EAAE,QAAQ,WAAW,GAAG,EAAE,SAAS;AAC/F;AACA,IAAI,QAAQ;AAAA,EACV,QAAQ;AAAA,EACR,KAAK;AAAA,EACL,UAAU;AACZ;AACA,IAAI,SAAS;AAAA,EACX,QAAQ;AAAA,EACR,KAAK;AAAA,EACL,QAAQ;AAAA,EACR,UAAU;AACZ;AAGA,IAAI,qBAAqB;AAAA,EACvB,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AACP;AACA,IAAI,uBAAuB,CAAC,OAAO,mBAAmB,EAAE;AACxD,SAAS,QAAQ,OAAO,QAAQ;AAC9B,MAAI,QAAQ;AACV,QAAI,MAAM,WAAW,KAAK,KAAK,GAAG;AAChC,aAAO,MAAM,QAAQ,MAAM,eAAe,oBAAoB;AAAA,IAChE;AAAA,EACF,OAAO;AACL,QAAI,MAAM,mBAAmB,KAAK,KAAK,GAAG;AACxC,aAAO,MAAM,QAAQ,MAAM,uBAAuB,oBAAoB;AAAA,IACxE;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,SAAS,MAAM;AACtB,MAAI;AACF,WAAO,UAAU,IAAI,EAAE,QAAQ,MAAM,eAAe,GAAG;AAAA,EACzD,QAAQ;AACN,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,SAAS,WAAW,UAAU,OAAO;AACnC,QAAM,MAAM,SAAS,QAAQ,MAAM,UAAU,CAAC,OAAO,QAAQ,QAAQ;AACnE,QAAI,UAAU;AACd,QAAI,OAAO;AACX,WAAO,EAAE,QAAQ,KAAK,IAAI,IAAI,MAAM,KAAM,WAAU,CAAC;AACrD,QAAI,SAAS;AACX,aAAO;AAAA,IACT,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF,CAAC,GAAG,QAAQ,IAAI,MAAM,MAAM,SAAS;AACrC,MAAI,IAAI;AACR,MAAI,CAAC,MAAM,CAAC,EAAE,KAAK,GAAG;AACpB,UAAM,MAAM;AAAA,EACd;AACA,MAAI,MAAM,SAAS,KAAK,CAAC,MAAM,GAAG,EAAE,GAAG,KAAK,GAAG;AAC7C,UAAM,IAAI;AAAA,EACZ;AACA,MAAI,OAAO;AACT,QAAI,MAAM,SAAS,OAAO;AACxB,YAAM,OAAO,KAAK;AAAA,IACpB,OAAO;AACL,aAAO,MAAM,SAAS,MAAO,OAAM,KAAK,EAAE;AAAA,IAC5C;AAAA,EACF;AACA,SAAO,IAAI,MAAM,QAAQ,KAAK;AAC5B,UAAM,CAAC,IAAI,MAAM,CAAC,EAAE,KAAK,EAAE,QAAQ,MAAM,WAAW,GAAG;AAAA,EACzD;AACA,SAAO;AACT;AACA,SAAS,MAAM,KAAK,GAAG,QAAQ;AAC7B,QAAM,IAAI,IAAI;AACd,MAAI,MAAM,GAAG;AACX,WAAO;AAAA,EACT;AACA,MAAI,UAAU;AACd,SAAO,UAAU,GAAG;AAClB,UAAM,WAAW,IAAI,OAAO,IAAI,UAAU,CAAC;AAC3C,QAAI,aAAa,KAAK,CAAC,QAAQ;AAC7B;AAAA,IACF,WAAW,aAAa,KAAK,QAAQ;AACnC;AAAA,IACF,OAAO;AACL;AAAA,IACF;AAAA,EACF;AACA,SAAO,IAAI,MAAM,GAAG,IAAI,OAAO;AACjC;AACA,SAAS,mBAAmB,KAAK,GAAG;AAClC,MAAI,IAAI,QAAQ,EAAE,CAAC,CAAC,MAAM,IAAI;AAC5B,WAAO;AAAA,EACT;AACA,MAAI,QAAQ;AACZ,WAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,QAAI,IAAI,CAAC,MAAM,MAAM;AACnB;AAAA,IACF,WAAW,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG;AAC1B;AAAA,IACF,WAAW,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG;AAC1B;AACA,UAAI,QAAQ,GAAG;AACb,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACA,MAAI,QAAQ,GAAG;AACb,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAGA,SAAS,WAAW,KAAK,OAAO,KAAK,QAAQ,OAAO;AAClD,QAAM,OAAO,MAAM;AACnB,QAAM,QAAQ,MAAM,SAAS;AAC7B,QAAM,OAAO,IAAI,CAAC,EAAE,QAAQ,MAAM,MAAM,mBAAmB,IAAI;AAC/D,SAAO,MAAM,SAAS;AACtB,QAAM,QAAQ;AAAA,IACZ,MAAM,IAAI,CAAC,EAAE,OAAO,CAAC,MAAM,MAAM,UAAU;AAAA,IAC3C;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,QAAQ,OAAO,aAAa,IAAI;AAAA,EAClC;AACA,SAAO,MAAM,SAAS;AACtB,SAAO;AACT;AACA,SAAS,uBAAuB,KAAK,MAAM,OAAO;AAChD,QAAM,oBAAoB,IAAI,MAAM,MAAM,MAAM,sBAAsB;AACtE,MAAI,sBAAsB,MAAM;AAC9B,WAAO;AAAA,EACT;AACA,QAAM,eAAe,kBAAkB,CAAC;AACxC,SAAO,KAAK,MAAM,IAAI,EAAE,IAAI,CAAC,SAAS;AACpC,UAAM,oBAAoB,KAAK,MAAM,MAAM,MAAM,cAAc;AAC/D,QAAI,sBAAsB,MAAM;AAC9B,aAAO;AAAA,IACT;AACA,UAAM,CAAC,YAAY,IAAI;AACvB,QAAI,aAAa,UAAU,aAAa,QAAQ;AAC9C,aAAO,KAAK,MAAM,aAAa,MAAM;AAAA,IACvC;AACA,WAAO;AAAA,EACT,CAAC,EAAE,KAAK,IAAI;AACd;AACA,IAAI,aAAa,MAAM;AAAA,EACrB;AAAA,EACA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,YAAY,UAAU;AACpB,SAAK,UAAU,YAAY;AAAA,EAC7B;AAAA,EACA,MAAM,KAAK;AACT,UAAM,MAAM,KAAK,MAAM,MAAM,QAAQ,KAAK,GAAG;AAC7C,QAAI,OAAO,IAAI,CAAC,EAAE,SAAS,GAAG;AAC5B,aAAO;AAAA,QACL,MAAM;AAAA,QACN,KAAK,IAAI,CAAC;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AAAA,EACA,KAAK,KAAK;AACR,UAAM,MAAM,KAAK,MAAM,MAAM,KAAK,KAAK,GAAG;AAC1C,QAAI,KAAK;AACP,YAAM,OAAO,IAAI,CAAC,EAAE,QAAQ,KAAK,MAAM,MAAM,kBAAkB,EAAE;AACjE,aAAO;AAAA,QACL,MAAM;AAAA,QACN,KAAK,IAAI,CAAC;AAAA,QACV,gBAAgB;AAAA,QAChB,MAAM,CAAC,KAAK,QAAQ,WAAW,MAAM,MAAM,IAAI,IAAI;AAAA,MACrD;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO,KAAK;AACV,UAAM,MAAM,KAAK,MAAM,MAAM,OAAO,KAAK,GAAG;AAC5C,QAAI,KAAK;AACP,YAAM,MAAM,IAAI,CAAC;AACjB,YAAM,OAAO,uBAAuB,KAAK,IAAI,CAAC,KAAK,IAAI,KAAK,KAAK;AACjE,aAAO;AAAA,QACL,MAAM;AAAA,QACN;AAAA,QACA,MAAM,IAAI,CAAC,IAAI,IAAI,CAAC,EAAE,KAAK,EAAE,QAAQ,KAAK,MAAM,OAAO,gBAAgB,IAAI,IAAI,IAAI,CAAC;AAAA,QACpF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,QAAQ,KAAK;AACX,UAAM,MAAM,KAAK,MAAM,MAAM,QAAQ,KAAK,GAAG;AAC7C,QAAI,KAAK;AACP,UAAI,OAAO,IAAI,CAAC,EAAE,KAAK;AACvB,UAAI,KAAK,MAAM,MAAM,WAAW,KAAK,IAAI,GAAG;AAC1C,cAAM,UAAU,MAAM,MAAM,GAAG;AAC/B,YAAI,KAAK,QAAQ,UAAU;AACzB,iBAAO,QAAQ,KAAK;AAAA,QACtB,WAAW,CAAC,WAAW,KAAK,MAAM,MAAM,gBAAgB,KAAK,OAAO,GAAG;AACrE,iBAAO,QAAQ,KAAK;AAAA,QACtB;AAAA,MACF;AACA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,KAAK,IAAI,CAAC;AAAA,QACV,OAAO,IAAI,CAAC,EAAE;AAAA,QACd;AAAA,QACA,QAAQ,KAAK,MAAM,OAAO,IAAI;AAAA,MAChC;AAAA,IACF;AAAA,EACF;AAAA,EACA,GAAG,KAAK;AACN,UAAM,MAAM,KAAK,MAAM,MAAM,GAAG,KAAK,GAAG;AACxC,QAAI,KAAK;AACP,aAAO;AAAA,QACL,MAAM;AAAA,QACN,KAAK,MAAM,IAAI,CAAC,GAAG,IAAI;AAAA,MACzB;AAAA,IACF;AAAA,EACF;AAAA,EACA,WAAW,KAAK;AACd,UAAM,MAAM,KAAK,MAAM,MAAM,WAAW,KAAK,GAAG;AAChD,QAAI,KAAK;AACP,UAAI,QAAQ,MAAM,IAAI,CAAC,GAAG,IAAI,EAAE,MAAM,IAAI;AAC1C,UAAI,MAAM;AACV,UAAI,OAAO;AACX,YAAM,SAAS,CAAC;AAChB,aAAO,MAAM,SAAS,GAAG;AACvB,YAAI,eAAe;AACnB,cAAM,eAAe,CAAC;AACtB,YAAI;AACJ,aAAK,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACjC,cAAI,KAAK,MAAM,MAAM,gBAAgB,KAAK,MAAM,CAAC,CAAC,GAAG;AACnD,yBAAa,KAAK,MAAM,CAAC,CAAC;AAC1B,2BAAe;AAAA,UACjB,WAAW,CAAC,cAAc;AACxB,yBAAa,KAAK,MAAM,CAAC,CAAC;AAAA,UAC5B,OAAO;AACL;AAAA,UACF;AAAA,QACF;AACA,gBAAQ,MAAM,MAAM,CAAC;AACrB,cAAM,aAAa,aAAa,KAAK,IAAI;AACzC,cAAM,cAAc,WAAW,QAAQ,KAAK,MAAM,MAAM,yBAAyB,UAAU,EAAE,QAAQ,KAAK,MAAM,MAAM,0BAA0B,EAAE;AAClJ,cAAM,MAAM,GAAG,GAAG;AAAA,EACxB,UAAU,KAAK;AACT,eAAO,OAAO,GAAG,IAAI;AAAA,EAC3B,WAAW,KAAK;AACV,cAAM,MAAM,KAAK,MAAM,MAAM;AAC7B,aAAK,MAAM,MAAM,MAAM;AACvB,aAAK,MAAM,YAAY,aAAa,QAAQ,IAAI;AAChD,aAAK,MAAM,MAAM,MAAM;AACvB,YAAI,MAAM,WAAW,GAAG;AACtB;AAAA,QACF;AACA,cAAM,YAAY,OAAO,GAAG,EAAE;AAC9B,YAAI,WAAW,SAAS,QAAQ;AAC9B;AAAA,QACF,WAAW,WAAW,SAAS,cAAc;AAC3C,gBAAM,WAAW;AACjB,gBAAM,UAAU,SAAS,MAAM,OAAO,MAAM,KAAK,IAAI;AACrD,gBAAM,WAAW,KAAK,WAAW,OAAO;AACxC,iBAAO,OAAO,SAAS,CAAC,IAAI;AAC5B,gBAAM,IAAI,UAAU,GAAG,IAAI,SAAS,SAAS,IAAI,MAAM,IAAI,SAAS;AACpE,iBAAO,KAAK,UAAU,GAAG,KAAK,SAAS,SAAS,KAAK,MAAM,IAAI,SAAS;AACxE;AAAA,QACF,WAAW,WAAW,SAAS,QAAQ;AACrC,gBAAM,WAAW;AACjB,gBAAM,UAAU,SAAS,MAAM,OAAO,MAAM,KAAK,IAAI;AACrD,gBAAM,WAAW,KAAK,KAAK,OAAO;AAClC,iBAAO,OAAO,SAAS,CAAC,IAAI;AAC5B,gBAAM,IAAI,UAAU,GAAG,IAAI,SAAS,UAAU,IAAI,MAAM,IAAI,SAAS;AACrE,iBAAO,KAAK,UAAU,GAAG,KAAK,SAAS,SAAS,IAAI,MAAM,IAAI,SAAS;AACvE,kBAAQ,QAAQ,UAAU,OAAO,GAAG,EAAE,EAAE,IAAI,MAAM,EAAE,MAAM,IAAI;AAC9D;AAAA,QACF;AAAA,MACF;AACA,aAAO;AAAA,QACL,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,KAAK,KAAK;AACR,QAAI,MAAM,KAAK,MAAM,MAAM,KAAK,KAAK,GAAG;AACxC,QAAI,KAAK;AACP,UAAI,OAAO,IAAI,CAAC,EAAE,KAAK;AACvB,YAAM,YAAY,KAAK,SAAS;AAChC,YAAM,QAAQ;AAAA,QACZ,MAAM;AAAA,QACN,KAAK;AAAA,QACL,SAAS;AAAA,QACT,OAAO,YAAY,CAAC,KAAK,MAAM,GAAG,EAAE,IAAI;AAAA,QACxC,OAAO;AAAA,QACP,OAAO,CAAC;AAAA,MACV;AACA,aAAO,YAAY,aAAa,KAAK,MAAM,EAAE,CAAC,KAAK,KAAK,IAAI;AAC5D,UAAI,KAAK,QAAQ,UAAU;AACzB,eAAO,YAAY,OAAO;AAAA,MAC5B;AACA,YAAM,YAAY,KAAK,MAAM,MAAM,cAAc,IAAI;AACrD,UAAI,oBAAoB;AACxB,aAAO,KAAK;AACV,YAAI,WAAW;AACf,YAAI,MAAM;AACV,YAAI,eAAe;AACnB,YAAI,EAAE,MAAM,UAAU,KAAK,GAAG,IAAI;AAChC;AAAA,QACF;AACA,YAAI,KAAK,MAAM,MAAM,GAAG,KAAK,GAAG,GAAG;AACjC;AAAA,QACF;AACA,cAAM,IAAI,CAAC;AACX,cAAM,IAAI,UAAU,IAAI,MAAM;AAC9B,YAAI,OAAO,IAAI,CAAC,EAAE,MAAM,MAAM,CAAC,EAAE,CAAC,EAAE,QAAQ,KAAK,MAAM,MAAM,iBAAiB,CAAC,MAAM,IAAI,OAAO,IAAI,EAAE,MAAM,CAAC;AAC7G,YAAI,WAAW,IAAI,MAAM,MAAM,CAAC,EAAE,CAAC;AACnC,YAAI,YAAY,CAAC,KAAK,KAAK;AAC3B,YAAI,SAAS;AACb,YAAI,KAAK,QAAQ,UAAU;AACzB,mBAAS;AACT,yBAAe,KAAK,UAAU;AAAA,QAChC,WAAW,WAAW;AACpB,mBAAS,IAAI,CAAC,EAAE,SAAS;AAAA,QAC3B,OAAO;AACL,mBAAS,IAAI,CAAC,EAAE,OAAO,KAAK,MAAM,MAAM,YAAY;AACpD,mBAAS,SAAS,IAAI,IAAI;AAC1B,yBAAe,KAAK,MAAM,MAAM;AAChC,oBAAU,IAAI,CAAC,EAAE;AAAA,QACnB;AACA,YAAI,aAAa,KAAK,MAAM,MAAM,UAAU,KAAK,QAAQ,GAAG;AAC1D,iBAAO,WAAW;AAClB,gBAAM,IAAI,UAAU,SAAS,SAAS,CAAC;AACvC,qBAAW;AAAA,QACb;AACA,YAAI,CAAC,UAAU;AACb,gBAAM,kBAAkB,KAAK,MAAM,MAAM,gBAAgB,MAAM;AAC/D,gBAAM,UAAU,KAAK,MAAM,MAAM,QAAQ,MAAM;AAC/C,gBAAM,mBAAmB,KAAK,MAAM,MAAM,iBAAiB,MAAM;AACjE,gBAAM,oBAAoB,KAAK,MAAM,MAAM,kBAAkB,MAAM;AACnE,gBAAM,iBAAiB,KAAK,MAAM,MAAM,eAAe,MAAM;AAC7D,iBAAO,KAAK;AACV,kBAAM,UAAU,IAAI,MAAM,MAAM,CAAC,EAAE,CAAC;AACpC,gBAAI;AACJ,uBAAW;AACX,gBAAI,KAAK,QAAQ,UAAU;AACzB,yBAAW,SAAS,QAAQ,KAAK,MAAM,MAAM,oBAAoB,IAAI;AACrE,oCAAsB;AAAA,YACxB,OAAO;AACL,oCAAsB,SAAS,QAAQ,KAAK,MAAM,MAAM,eAAe,MAAM;AAAA,YAC/E;AACA,gBAAI,iBAAiB,KAAK,QAAQ,GAAG;AACnC;AAAA,YACF;AACA,gBAAI,kBAAkB,KAAK,QAAQ,GAAG;AACpC;AAAA,YACF;AACA,gBAAI,eAAe,KAAK,QAAQ,GAAG;AACjC;AAAA,YACF;AACA,gBAAI,gBAAgB,KAAK,QAAQ,GAAG;AAClC;AAAA,YACF;AACA,gBAAI,QAAQ,KAAK,QAAQ,GAAG;AAC1B;AAAA,YACF;AACA,gBAAI,oBAAoB,OAAO,KAAK,MAAM,MAAM,YAAY,KAAK,UAAU,CAAC,SAAS,KAAK,GAAG;AAC3F,8BAAgB,OAAO,oBAAoB,MAAM,MAAM;AAAA,YACzD,OAAO;AACL,kBAAI,WAAW;AACb;AAAA,cACF;AACA,kBAAI,KAAK,QAAQ,KAAK,MAAM,MAAM,eAAe,MAAM,EAAE,OAAO,KAAK,MAAM,MAAM,YAAY,KAAK,GAAG;AACnG;AAAA,cACF;AACA,kBAAI,iBAAiB,KAAK,IAAI,GAAG;AAC/B;AAAA,cACF;AACA,kBAAI,kBAAkB,KAAK,IAAI,GAAG;AAChC;AAAA,cACF;AACA,kBAAI,QAAQ,KAAK,IAAI,GAAG;AACtB;AAAA,cACF;AACA,8BAAgB,OAAO;AAAA,YACzB;AACA,gBAAI,CAAC,aAAa,CAAC,SAAS,KAAK,GAAG;AAClC,0BAAY;AAAA,YACd;AACA,mBAAO,UAAU;AACjB,kBAAM,IAAI,UAAU,QAAQ,SAAS,CAAC;AACtC,mBAAO,oBAAoB,MAAM,MAAM;AAAA,UACzC;AAAA,QACF;AACA,YAAI,CAAC,MAAM,OAAO;AAChB,cAAI,mBAAmB;AACrB,kBAAM,QAAQ;AAAA,UAChB,WAAW,KAAK,MAAM,MAAM,gBAAgB,KAAK,GAAG,GAAG;AACrD,gCAAoB;AAAA,UACtB;AAAA,QACF;AACA,YAAI,SAAS;AACb,YAAI;AACJ,YAAI,KAAK,QAAQ,KAAK;AACpB,mBAAS,KAAK,MAAM,MAAM,WAAW,KAAK,YAAY;AACtD,cAAI,QAAQ;AACV,wBAAY,OAAO,CAAC,MAAM;AAC1B,2BAAe,aAAa,QAAQ,KAAK,MAAM,MAAM,iBAAiB,EAAE;AAAA,UAC1E;AAAA,QACF;AACA,cAAM,MAAM,KAAK;AAAA,UACf,MAAM;AAAA,UACN;AAAA,UACA,MAAM,CAAC,CAAC;AAAA,UACR,SAAS;AAAA,UACT,OAAO;AAAA,UACP,MAAM;AAAA,UACN,QAAQ,CAAC;AAAA,QACX,CAAC;AACD,cAAM,OAAO;AAAA,MACf;AACA,YAAM,WAAW,MAAM,MAAM,GAAG,EAAE;AAClC,UAAI,UAAU;AACZ,iBAAS,MAAM,SAAS,IAAI,QAAQ;AACpC,iBAAS,OAAO,SAAS,KAAK,QAAQ;AAAA,MACxC,OAAO;AACL;AAAA,MACF;AACA,YAAM,MAAM,MAAM,IAAI,QAAQ;AAC9B,eAAS,IAAI,GAAG,IAAI,MAAM,MAAM,QAAQ,KAAK;AAC3C,aAAK,MAAM,MAAM,MAAM;AACvB,cAAM,MAAM,CAAC,EAAE,SAAS,KAAK,MAAM,YAAY,MAAM,MAAM,CAAC,EAAE,MAAM,CAAC,CAAC;AACtE,YAAI,CAAC,MAAM,OAAO;AAChB,gBAAM,UAAU,MAAM,MAAM,CAAC,EAAE,OAAO,OAAO,CAAC,MAAM,EAAE,SAAS,OAAO;AACtE,gBAAM,wBAAwB,QAAQ,SAAS,KAAK,QAAQ,KAAK,CAAC,MAAM,KAAK,MAAM,MAAM,QAAQ,KAAK,EAAE,GAAG,CAAC;AAC5G,gBAAM,QAAQ;AAAA,QAChB;AAAA,MACF;AACA,UAAI,MAAM,OAAO;AACf,iBAAS,IAAI,GAAG,IAAI,MAAM,MAAM,QAAQ,KAAK;AAC3C,gBAAM,MAAM,CAAC,EAAE,QAAQ;AAAA,QACzB;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,KAAK,KAAK;AACR,UAAM,MAAM,KAAK,MAAM,MAAM,KAAK,KAAK,GAAG;AAC1C,QAAI,KAAK;AACP,YAAM,QAAQ;AAAA,QACZ,MAAM;AAAA,QACN,OAAO;AAAA,QACP,KAAK,IAAI,CAAC;AAAA,QACV,KAAK,IAAI,CAAC,MAAM,SAAS,IAAI,CAAC,MAAM,YAAY,IAAI,CAAC,MAAM;AAAA,QAC3D,MAAM,IAAI,CAAC;AAAA,MACb;AACA,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,IAAI,KAAK;AACP,UAAM,MAAM,KAAK,MAAM,MAAM,IAAI,KAAK,GAAG;AACzC,QAAI,KAAK;AACP,YAAM,OAAO,IAAI,CAAC,EAAE,YAAY,EAAE,QAAQ,KAAK,MAAM,MAAM,qBAAqB,GAAG;AACnF,YAAM,OAAO,IAAI,CAAC,IAAI,IAAI,CAAC,EAAE,QAAQ,KAAK,MAAM,MAAM,cAAc,IAAI,EAAE,QAAQ,KAAK,MAAM,OAAO,gBAAgB,IAAI,IAAI;AAC5H,YAAM,QAAQ,IAAI,CAAC,IAAI,IAAI,CAAC,EAAE,UAAU,GAAG,IAAI,CAAC,EAAE,SAAS,CAAC,EAAE,QAAQ,KAAK,MAAM,OAAO,gBAAgB,IAAI,IAAI,IAAI,CAAC;AACrH,aAAO;AAAA,QACL,MAAM;AAAA,QACN,KAAK;AAAA,QACL,KAAK,IAAI,CAAC;AAAA,QACV;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,MAAM,KAAK;AACT,UAAM,MAAM,KAAK,MAAM,MAAM,MAAM,KAAK,GAAG;AAC3C,QAAI,CAAC,KAAK;AACR;AAAA,IACF;AACA,QAAI,CAAC,KAAK,MAAM,MAAM,eAAe,KAAK,IAAI,CAAC,CAAC,GAAG;AACjD;AAAA,IACF;AACA,UAAM,UAAU,WAAW,IAAI,CAAC,CAAC;AACjC,UAAM,SAAS,IAAI,CAAC,EAAE,QAAQ,KAAK,MAAM,MAAM,iBAAiB,EAAE,EAAE,MAAM,GAAG;AAC7E,UAAM,OAAO,IAAI,CAAC,GAAG,KAAK,IAAI,IAAI,CAAC,EAAE,QAAQ,KAAK,MAAM,MAAM,mBAAmB,EAAE,EAAE,MAAM,IAAI,IAAI,CAAC;AACpG,UAAM,OAAO;AAAA,MACX,MAAM;AAAA,MACN,KAAK,IAAI,CAAC;AAAA,MACV,QAAQ,CAAC;AAAA,MACT,OAAO,CAAC;AAAA,MACR,MAAM,CAAC;AAAA,IACT;AACA,QAAI,QAAQ,WAAW,OAAO,QAAQ;AACpC;AAAA,IACF;AACA,eAAW,SAAS,QAAQ;AAC1B,UAAI,KAAK,MAAM,MAAM,gBAAgB,KAAK,KAAK,GAAG;AAChD,aAAK,MAAM,KAAK,OAAO;AAAA,MACzB,WAAW,KAAK,MAAM,MAAM,iBAAiB,KAAK,KAAK,GAAG;AACxD,aAAK,MAAM,KAAK,QAAQ;AAAA,MAC1B,WAAW,KAAK,MAAM,MAAM,eAAe,KAAK,KAAK,GAAG;AACtD,aAAK,MAAM,KAAK,MAAM;AAAA,MACxB,OAAO;AACL,aAAK,MAAM,KAAK,IAAI;AAAA,MACtB;AAAA,IACF;AACA,aAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,WAAK,OAAO,KAAK;AAAA,QACf,MAAM,QAAQ,CAAC;AAAA,QACf,QAAQ,KAAK,MAAM,OAAO,QAAQ,CAAC,CAAC;AAAA,QACpC,QAAQ;AAAA,QACR,OAAO,KAAK,MAAM,CAAC;AAAA,MACrB,CAAC;AAAA,IACH;AACA,eAAW,OAAO,MAAM;AACtB,WAAK,KAAK,KAAK,WAAW,KAAK,KAAK,OAAO,MAAM,EAAE,IAAI,CAAC,MAAM,MAAM;AAClE,eAAO;AAAA,UACL,MAAM;AAAA,UACN,QAAQ,KAAK,MAAM,OAAO,IAAI;AAAA,UAC9B,QAAQ;AAAA,UACR,OAAO,KAAK,MAAM,CAAC;AAAA,QACrB;AAAA,MACF,CAAC,CAAC;AAAA,IACJ;AACA,WAAO;AAAA,EACT;AAAA,EACA,SAAS,KAAK;AACZ,UAAM,MAAM,KAAK,MAAM,MAAM,SAAS,KAAK,GAAG;AAC9C,QAAI,KAAK;AACP,aAAO;AAAA,QACL,MAAM;AAAA,QACN,KAAK,IAAI,CAAC;AAAA,QACV,OAAO,IAAI,CAAC,EAAE,OAAO,CAAC,MAAM,MAAM,IAAI;AAAA,QACtC,MAAM,IAAI,CAAC;AAAA,QACX,QAAQ,KAAK,MAAM,OAAO,IAAI,CAAC,CAAC;AAAA,MAClC;AAAA,IACF;AAAA,EACF;AAAA,EACA,UAAU,KAAK;AACb,UAAM,MAAM,KAAK,MAAM,MAAM,UAAU,KAAK,GAAG;AAC/C,QAAI,KAAK;AACP,YAAM,OAAO,IAAI,CAAC,EAAE,OAAO,IAAI,CAAC,EAAE,SAAS,CAAC,MAAM,OAAO,IAAI,CAAC,EAAE,MAAM,GAAG,EAAE,IAAI,IAAI,CAAC;AACpF,aAAO;AAAA,QACL,MAAM;AAAA,QACN,KAAK,IAAI,CAAC;AAAA,QACV;AAAA,QACA,QAAQ,KAAK,MAAM,OAAO,IAAI;AAAA,MAChC;AAAA,IACF;AAAA,EACF;AAAA,EACA,KAAK,KAAK;AACR,UAAM,MAAM,KAAK,MAAM,MAAM,KAAK,KAAK,GAAG;AAC1C,QAAI,KAAK;AACP,aAAO;AAAA,QACL,MAAM;AAAA,QACN,KAAK,IAAI,CAAC;AAAA,QACV,MAAM,IAAI,CAAC;AAAA,QACX,QAAQ,KAAK,MAAM,OAAO,IAAI,CAAC,CAAC;AAAA,MAClC;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO,KAAK;AACV,UAAM,MAAM,KAAK,MAAM,OAAO,OAAO,KAAK,GAAG;AAC7C,QAAI,KAAK;AACP,aAAO;AAAA,QACL,MAAM;AAAA,QACN,KAAK,IAAI,CAAC;AAAA,QACV,MAAM,IAAI,CAAC;AAAA,MACb;AAAA,IACF;AAAA,EACF;AAAA,EACA,IAAI,KAAK;AACP,UAAM,MAAM,KAAK,MAAM,OAAO,IAAI,KAAK,GAAG;AAC1C,QAAI,KAAK;AACP,UAAI,CAAC,KAAK,MAAM,MAAM,UAAU,KAAK,MAAM,MAAM,UAAU,KAAK,IAAI,CAAC,CAAC,GAAG;AACvE,aAAK,MAAM,MAAM,SAAS;AAAA,MAC5B,WAAW,KAAK,MAAM,MAAM,UAAU,KAAK,MAAM,MAAM,QAAQ,KAAK,IAAI,CAAC,CAAC,GAAG;AAC3E,aAAK,MAAM,MAAM,SAAS;AAAA,MAC5B;AACA,UAAI,CAAC,KAAK,MAAM,MAAM,cAAc,KAAK,MAAM,MAAM,kBAAkB,KAAK,IAAI,CAAC,CAAC,GAAG;AACnF,aAAK,MAAM,MAAM,aAAa;AAAA,MAChC,WAAW,KAAK,MAAM,MAAM,cAAc,KAAK,MAAM,MAAM,gBAAgB,KAAK,IAAI,CAAC,CAAC,GAAG;AACvF,aAAK,MAAM,MAAM,aAAa;AAAA,MAChC;AACA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,KAAK,IAAI,CAAC;AAAA,QACV,QAAQ,KAAK,MAAM,MAAM;AAAA,QACzB,YAAY,KAAK,MAAM,MAAM;AAAA,QAC7B,OAAO;AAAA,QACP,MAAM,IAAI,CAAC;AAAA,MACb;AAAA,IACF;AAAA,EACF;AAAA,EACA,KAAK,KAAK;AACR,UAAM,MAAM,KAAK,MAAM,OAAO,KAAK,KAAK,GAAG;AAC3C,QAAI,KAAK;AACP,YAAM,aAAa,IAAI,CAAC,EAAE,KAAK;AAC/B,UAAI,CAAC,KAAK,QAAQ,YAAY,KAAK,MAAM,MAAM,kBAAkB,KAAK,UAAU,GAAG;AACjF,YAAI,CAAC,KAAK,MAAM,MAAM,gBAAgB,KAAK,UAAU,GAAG;AACtD;AAAA,QACF;AACA,cAAM,aAAa,MAAM,WAAW,MAAM,GAAG,EAAE,GAAG,IAAI;AACtD,aAAK,WAAW,SAAS,WAAW,UAAU,MAAM,GAAG;AACrD;AAAA,QACF;AAAA,MACF,OAAO;AACL,cAAM,iBAAiB,mBAAmB,IAAI,CAAC,GAAG,IAAI;AACtD,YAAI,mBAAmB,IAAI;AACzB;AAAA,QACF;AACA,YAAI,iBAAiB,IAAI;AACvB,gBAAM,QAAQ,IAAI,CAAC,EAAE,QAAQ,GAAG,MAAM,IAAI,IAAI;AAC9C,gBAAM,UAAU,QAAQ,IAAI,CAAC,EAAE,SAAS;AACxC,cAAI,CAAC,IAAI,IAAI,CAAC,EAAE,UAAU,GAAG,cAAc;AAC3C,cAAI,CAAC,IAAI,IAAI,CAAC,EAAE,UAAU,GAAG,OAAO,EAAE,KAAK;AAC3C,cAAI,CAAC,IAAI;AAAA,QACX;AAAA,MACF;AACA,UAAI,OAAO,IAAI,CAAC;AAChB,UAAI,QAAQ;AACZ,UAAI,KAAK,QAAQ,UAAU;AACzB,cAAM,QAAQ,KAAK,MAAM,MAAM,kBAAkB,KAAK,IAAI;AAC1D,YAAI,OAAO;AACT,iBAAO,MAAM,CAAC;AACd,kBAAQ,MAAM,CAAC;AAAA,QACjB;AAAA,MACF,OAAO;AACL,gBAAQ,IAAI,CAAC,IAAI,IAAI,CAAC,EAAE,MAAM,GAAG,EAAE,IAAI;AAAA,MACzC;AACA,aAAO,KAAK,KAAK;AACjB,UAAI,KAAK,MAAM,MAAM,kBAAkB,KAAK,IAAI,GAAG;AACjD,YAAI,KAAK,QAAQ,YAAY,CAAC,KAAK,MAAM,MAAM,gBAAgB,KAAK,UAAU,GAAG;AAC/E,iBAAO,KAAK,MAAM,CAAC;AAAA,QACrB,OAAO;AACL,iBAAO,KAAK,MAAM,GAAG,EAAE;AAAA,QACzB;AAAA,MACF;AACA,aAAO,WAAW,KAAK;AAAA,QACrB,MAAM,OAAO,KAAK,QAAQ,KAAK,MAAM,OAAO,gBAAgB,IAAI,IAAI;AAAA,QACpE,OAAO,QAAQ,MAAM,QAAQ,KAAK,MAAM,OAAO,gBAAgB,IAAI,IAAI;AAAA,MACzE,GAAG,IAAI,CAAC,GAAG,KAAK,OAAO,KAAK,KAAK;AAAA,IACnC;AAAA,EACF;AAAA,EACA,QAAQ,KAAK,OAAO;AAClB,QAAI;AACJ,SAAK,MAAM,KAAK,MAAM,OAAO,QAAQ,KAAK,GAAG,OAAO,MAAM,KAAK,MAAM,OAAO,OAAO,KAAK,GAAG,IAAI;AAC7F,YAAM,cAAc,IAAI,CAAC,KAAK,IAAI,CAAC,GAAG,QAAQ,KAAK,MAAM,MAAM,qBAAqB,GAAG;AACvF,YAAM,QAAQ,MAAM,WAAW,YAAY,CAAC;AAC5C,UAAI,CAAC,OAAO;AACV,cAAM,OAAO,IAAI,CAAC,EAAE,OAAO,CAAC;AAC5B,eAAO;AAAA,UACL,MAAM;AAAA,UACN,KAAK;AAAA,UACL;AAAA,QACF;AAAA,MACF;AACA,aAAO,WAAW,KAAK,OAAO,IAAI,CAAC,GAAG,KAAK,OAAO,KAAK,KAAK;AAAA,IAC9D;AAAA,EACF;AAAA,EACA,SAAS,KAAK,WAAW,WAAW,IAAI;AACtC,QAAI,QAAQ,KAAK,MAAM,OAAO,eAAe,KAAK,GAAG;AACrD,QAAI,CAAC,MAAO;AACZ,QAAI,MAAM,CAAC,KAAK,SAAS,MAAM,KAAK,MAAM,MAAM,mBAAmB,EAAG;AACtE,UAAM,WAAW,MAAM,CAAC,KAAK,MAAM,CAAC,KAAK;AACzC,QAAI,CAAC,YAAY,CAAC,YAAY,KAAK,MAAM,OAAO,YAAY,KAAK,QAAQ,GAAG;AAC1E,YAAM,UAAU,CAAC,GAAG,MAAM,CAAC,CAAC,EAAE,SAAS;AACvC,UAAI,QAAQ,SAAS,aAAa,SAAS,gBAAgB;AAC3D,YAAM,SAAS,MAAM,CAAC,EAAE,CAAC,MAAM,MAAM,KAAK,MAAM,OAAO,oBAAoB,KAAK,MAAM,OAAO;AAC7F,aAAO,YAAY;AACnB,kBAAY,UAAU,MAAM,KAAK,IAAI,SAAS,OAAO;AACrD,cAAQ,QAAQ,OAAO,KAAK,SAAS,MAAM,MAAM;AAC/C,iBAAS,MAAM,CAAC,KAAK,MAAM,CAAC,KAAK,MAAM,CAAC,KAAK,MAAM,CAAC,KAAK,MAAM,CAAC,KAAK,MAAM,CAAC;AAC5E,YAAI,CAAC,OAAQ;AACb,kBAAU,CAAC,GAAG,MAAM,EAAE;AACtB,YAAI,MAAM,CAAC,KAAK,MAAM,CAAC,GAAG;AACxB,wBAAc;AACd;AAAA,QACF,WAAW,MAAM,CAAC,KAAK,MAAM,CAAC,GAAG;AAC/B,cAAI,UAAU,KAAK,GAAG,UAAU,WAAW,IAAI;AAC7C,6BAAiB;AACjB;AAAA,UACF;AAAA,QACF;AACA,sBAAc;AACd,YAAI,aAAa,EAAG;AACpB,kBAAU,KAAK,IAAI,SAAS,UAAU,aAAa,aAAa;AAChE,cAAM,iBAAiB,CAAC,GAAG,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE;AACxC,cAAM,MAAM,IAAI,MAAM,GAAG,UAAU,MAAM,QAAQ,iBAAiB,OAAO;AACzE,YAAI,KAAK,IAAI,SAAS,OAAO,IAAI,GAAG;AAClC,gBAAM,QAAQ,IAAI,MAAM,GAAG,EAAE;AAC7B,iBAAO;AAAA,YACL,MAAM;AAAA,YACN;AAAA,YACA,MAAM;AAAA,YACN,QAAQ,KAAK,MAAM,aAAa,KAAK;AAAA,UACvC;AAAA,QACF;AACA,cAAM,OAAO,IAAI,MAAM,GAAG,EAAE;AAC5B,eAAO;AAAA,UACL,MAAM;AAAA,UACN;AAAA,UACA;AAAA,UACA,QAAQ,KAAK,MAAM,aAAa,IAAI;AAAA,QACtC;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,SAAS,KAAK;AACZ,UAAM,MAAM,KAAK,MAAM,OAAO,KAAK,KAAK,GAAG;AAC3C,QAAI,KAAK;AACP,UAAI,OAAO,IAAI,CAAC,EAAE,QAAQ,KAAK,MAAM,MAAM,mBAAmB,GAAG;AACjE,YAAM,mBAAmB,KAAK,MAAM,MAAM,aAAa,KAAK,IAAI;AAChE,YAAM,0BAA0B,KAAK,MAAM,MAAM,kBAAkB,KAAK,IAAI,KAAK,KAAK,MAAM,MAAM,gBAAgB,KAAK,IAAI;AAC3H,UAAI,oBAAoB,yBAAyB;AAC/C,eAAO,KAAK,UAAU,GAAG,KAAK,SAAS,CAAC;AAAA,MAC1C;AACA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,KAAK,IAAI,CAAC;AAAA,QACV;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,GAAG,KAAK;AACN,UAAM,MAAM,KAAK,MAAM,OAAO,GAAG,KAAK,GAAG;AACzC,QAAI,KAAK;AACP,aAAO;AAAA,QACL,MAAM;AAAA,QACN,KAAK,IAAI,CAAC;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AAAA,EACA,IAAI,KAAK;AACP,UAAM,MAAM,KAAK,MAAM,OAAO,IAAI,KAAK,GAAG;AAC1C,QAAI,KAAK;AACP,aAAO;AAAA,QACL,MAAM;AAAA,QACN,KAAK,IAAI,CAAC;AAAA,QACV,MAAM,IAAI,CAAC;AAAA,QACX,QAAQ,KAAK,MAAM,aAAa,IAAI,CAAC,CAAC;AAAA,MACxC;AAAA,IACF;AAAA,EACF;AAAA,EACA,SAAS,KAAK;AACZ,UAAM,MAAM,KAAK,MAAM,OAAO,SAAS,KAAK,GAAG;AAC/C,QAAI,KAAK;AACP,UAAI,MAAM;AACV,UAAI,IAAI,CAAC,MAAM,KAAK;AAClB,eAAO,IAAI,CAAC;AACZ,eAAO,YAAY;AAAA,MACrB,OAAO;AACL,eAAO,IAAI,CAAC;AACZ,eAAO;AAAA,MACT;AACA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,KAAK,IAAI,CAAC;AAAA,QACV;AAAA,QACA;AAAA,QACA,QAAQ;AAAA,UACN;AAAA,YACE,MAAM;AAAA,YACN,KAAK;AAAA,YACL;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,IAAI,KAAK;AACP,QAAI;AACJ,QAAI,MAAM,KAAK,MAAM,OAAO,IAAI,KAAK,GAAG,GAAG;AACzC,UAAI,MAAM;AACV,UAAI,IAAI,CAAC,MAAM,KAAK;AAClB,eAAO,IAAI,CAAC;AACZ,eAAO,YAAY;AAAA,MACrB,OAAO;AACL,YAAI;AACJ,WAAG;AACD,wBAAc,IAAI,CAAC;AACnB,cAAI,CAAC,IAAI,KAAK,MAAM,OAAO,WAAW,KAAK,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK;AAAA,QAC7D,SAAS,gBAAgB,IAAI,CAAC;AAC9B,eAAO,IAAI,CAAC;AACZ,YAAI,IAAI,CAAC,MAAM,QAAQ;AACrB,iBAAO,YAAY,IAAI,CAAC;AAAA,QAC1B,OAAO;AACL,iBAAO,IAAI,CAAC;AAAA,QACd;AAAA,MACF;AACA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,KAAK,IAAI,CAAC;AAAA,QACV;AAAA,QACA;AAAA,QACA,QAAQ;AAAA,UACN;AAAA,YACE,MAAM;AAAA,YACN,KAAK;AAAA,YACL;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,WAAW,KAAK;AACd,UAAM,MAAM,KAAK,MAAM,OAAO,KAAK,KAAK,GAAG;AAC3C,QAAI,KAAK;AACP,YAAM,UAAU,KAAK,MAAM,MAAM;AACjC,aAAO;AAAA,QACL,MAAM;AAAA,QACN,KAAK,IAAI,CAAC;AAAA,QACV,MAAM,IAAI,CAAC;AAAA,QACX;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAGA,IAAI,SAAS,MAAM,QAAQ;AAAA,EACzB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAY,UAAU;AACpB,SAAK,SAAS,CAAC;AACf,SAAK,OAAO,QAAwB,uBAAO,OAAO,IAAI;AACtD,SAAK,UAAU,YAAY;AAC3B,SAAK,QAAQ,YAAY,KAAK,QAAQ,aAAa,IAAI,WAAW;AAClE,SAAK,YAAY,KAAK,QAAQ;AAC9B,SAAK,UAAU,UAAU,KAAK;AAC9B,SAAK,UAAU,QAAQ;AACvB,SAAK,cAAc,CAAC;AACpB,SAAK,QAAQ;AAAA,MACX,QAAQ;AAAA,MACR,YAAY;AAAA,MACZ,KAAK;AAAA,IACP;AACA,UAAM,QAAQ;AAAA,MACZ;AAAA,MACA,OAAO,MAAM;AAAA,MACb,QAAQ,OAAO;AAAA,IACjB;AACA,QAAI,KAAK,QAAQ,UAAU;AACzB,YAAM,QAAQ,MAAM;AACpB,YAAM,SAAS,OAAO;AAAA,IACxB,WAAW,KAAK,QAAQ,KAAK;AAC3B,YAAM,QAAQ,MAAM;AACpB,UAAI,KAAK,QAAQ,QAAQ;AACvB,cAAM,SAAS,OAAO;AAAA,MACxB,OAAO;AACL,cAAM,SAAS,OAAO;AAAA,MACxB;AAAA,IACF;AACA,SAAK,UAAU,QAAQ;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW,QAAQ;AACjB,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,IAAI,KAAK,UAAU;AACxB,UAAM,SAAS,IAAI,QAAQ,QAAQ;AACnC,WAAO,OAAO,IAAI,GAAG;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,UAAU,KAAK,UAAU;AAC9B,UAAM,SAAS,IAAI,QAAQ,QAAQ;AACnC,WAAO,OAAO,aAAa,GAAG;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,KAAK;AACP,UAAM,IAAI,QAAQ,MAAM,gBAAgB,IAAI;AAC5C,SAAK,YAAY,KAAK,KAAK,MAAM;AACjC,aAAS,IAAI,GAAG,IAAI,KAAK,YAAY,QAAQ,KAAK;AAChD,YAAM,OAAO,KAAK,YAAY,CAAC;AAC/B,WAAK,aAAa,KAAK,KAAK,KAAK,MAAM;AAAA,IACzC;AACA,SAAK,cAAc,CAAC;AACpB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,YAAY,KAAK,SAAS,CAAC,GAAG,uBAAuB,OAAO;AAC1D,QAAI,KAAK,QAAQ,UAAU;AACzB,YAAM,IAAI,QAAQ,MAAM,eAAe,MAAM,EAAE,QAAQ,MAAM,WAAW,EAAE;AAAA,IAC5E;AACA,WAAO,KAAK;AACV,UAAI;AACJ,UAAI,KAAK,QAAQ,YAAY,OAAO,KAAK,CAAC,iBAAiB;AACzD,YAAI,QAAQ,aAAa,KAAK,EAAE,OAAO,KAAK,GAAG,KAAK,MAAM,GAAG;AAC3D,gBAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,iBAAO,KAAK,KAAK;AACjB,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT,CAAC,GAAG;AACF;AAAA,MACF;AACA,UAAI,QAAQ,KAAK,UAAU,MAAM,GAAG,GAAG;AACrC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,cAAM,YAAY,OAAO,GAAG,EAAE;AAC9B,YAAI,MAAM,IAAI,WAAW,KAAK,cAAc,QAAQ;AAClD,oBAAU,OAAO;AAAA,QACnB,OAAO;AACL,iBAAO,KAAK,KAAK;AAAA,QACnB;AACA;AAAA,MACF;AACA,UAAI,QAAQ,KAAK,UAAU,KAAK,GAAG,GAAG;AACpC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,cAAM,YAAY,OAAO,GAAG,EAAE;AAC9B,YAAI,WAAW,SAAS,eAAe,WAAW,SAAS,QAAQ;AACjE,oBAAU,OAAO,OAAO,MAAM;AAC9B,oBAAU,QAAQ,OAAO,MAAM;AAC/B,eAAK,YAAY,GAAG,EAAE,EAAE,MAAM,UAAU;AAAA,QAC1C,OAAO;AACL,iBAAO,KAAK,KAAK;AAAA,QACnB;AACA;AAAA,MACF;AACA,UAAI,QAAQ,KAAK,UAAU,OAAO,GAAG,GAAG;AACtC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;AAAA,MACF;AACA,UAAI,QAAQ,KAAK,UAAU,QAAQ,GAAG,GAAG;AACvC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;AAAA,MACF;AACA,UAAI,QAAQ,KAAK,UAAU,GAAG,GAAG,GAAG;AAClC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;AAAA,MACF;AACA,UAAI,QAAQ,KAAK,UAAU,WAAW,GAAG,GAAG;AAC1C,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;AAAA,MACF;AACA,UAAI,QAAQ,KAAK,UAAU,KAAK,GAAG,GAAG;AACpC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;AAAA,MACF;AACA,UAAI,QAAQ,KAAK,UAAU,KAAK,GAAG,GAAG;AACpC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;AAAA,MACF;AACA,UAAI,QAAQ,KAAK,UAAU,IAAI,GAAG,GAAG;AACnC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,cAAM,YAAY,OAAO,GAAG,EAAE;AAC9B,YAAI,WAAW,SAAS,eAAe,WAAW,SAAS,QAAQ;AACjE,oBAAU,OAAO,OAAO,MAAM;AAC9B,oBAAU,QAAQ,OAAO,MAAM;AAC/B,eAAK,YAAY,GAAG,EAAE,EAAE,MAAM,UAAU;AAAA,QAC1C,WAAW,CAAC,KAAK,OAAO,MAAM,MAAM,GAAG,GAAG;AACxC,eAAK,OAAO,MAAM,MAAM,GAAG,IAAI;AAAA,YAC7B,MAAM,MAAM;AAAA,YACZ,OAAO,MAAM;AAAA,UACf;AAAA,QACF;AACA;AAAA,MACF;AACA,UAAI,QAAQ,KAAK,UAAU,MAAM,GAAG,GAAG;AACrC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;AAAA,MACF;AACA,UAAI,QAAQ,KAAK,UAAU,SAAS,GAAG,GAAG;AACxC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;AAAA,MACF;AACA,UAAI,SAAS;AACb,UAAI,KAAK,QAAQ,YAAY,YAAY;AACvC,YAAI,aAAa;AACjB,cAAM,UAAU,IAAI,MAAM,CAAC;AAC3B,YAAI;AACJ,aAAK,QAAQ,WAAW,WAAW,QAAQ,CAAC,kBAAkB;AAC5D,sBAAY,cAAc,KAAK,EAAE,OAAO,KAAK,GAAG,OAAO;AACvD,cAAI,OAAO,cAAc,YAAY,aAAa,GAAG;AACnD,yBAAa,KAAK,IAAI,YAAY,SAAS;AAAA,UAC7C;AAAA,QACF,CAAC;AACD,YAAI,aAAa,YAAY,cAAc,GAAG;AAC5C,mBAAS,IAAI,UAAU,GAAG,aAAa,CAAC;AAAA,QAC1C;AAAA,MACF;AACA,UAAI,KAAK,MAAM,QAAQ,QAAQ,KAAK,UAAU,UAAU,MAAM,IAAI;AAChE,cAAM,YAAY,OAAO,GAAG,EAAE;AAC9B,YAAI,wBAAwB,WAAW,SAAS,aAAa;AAC3D,oBAAU,OAAO,OAAO,MAAM;AAC9B,oBAAU,QAAQ,OAAO,MAAM;AAC/B,eAAK,YAAY,IAAI;AACrB,eAAK,YAAY,GAAG,EAAE,EAAE,MAAM,UAAU;AAAA,QAC1C,OAAO;AACL,iBAAO,KAAK,KAAK;AAAA,QACnB;AACA,+BAAuB,OAAO,WAAW,IAAI;AAC7C,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC;AAAA,MACF;AACA,UAAI,QAAQ,KAAK,UAAU,KAAK,GAAG,GAAG;AACpC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,cAAM,YAAY,OAAO,GAAG,EAAE;AAC9B,YAAI,WAAW,SAAS,QAAQ;AAC9B,oBAAU,OAAO,OAAO,MAAM;AAC9B,oBAAU,QAAQ,OAAO,MAAM;AAC/B,eAAK,YAAY,IAAI;AACrB,eAAK,YAAY,GAAG,EAAE,EAAE,MAAM,UAAU;AAAA,QAC1C,OAAO;AACL,iBAAO,KAAK,KAAK;AAAA,QACnB;AACA;AAAA,MACF;AACA,UAAI,KAAK;AACP,cAAM,SAAS,4BAA4B,IAAI,WAAW,CAAC;AAC3D,YAAI,KAAK,QAAQ,QAAQ;AACvB,kBAAQ,MAAM,MAAM;AACpB;AAAA,QACF,OAAO;AACL,gBAAM,IAAI,MAAM,MAAM;AAAA,QACxB;AAAA,MACF;AAAA,IACF;AACA,SAAK,MAAM,MAAM;AACjB,WAAO;AAAA,EACT;AAAA,EACA,OAAO,KAAK,SAAS,CAAC,GAAG;AACvB,SAAK,YAAY,KAAK,EAAE,KAAK,OAAO,CAAC;AACrC,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAIA,aAAa,KAAK,SAAS,CAAC,GAAG;AAC7B,QAAI,YAAY;AAChB,QAAI,QAAQ;AACZ,QAAI,KAAK,OAAO,OAAO;AACrB,YAAM,QAAQ,OAAO,KAAK,KAAK,OAAO,KAAK;AAC3C,UAAI,MAAM,SAAS,GAAG;AACpB,gBAAQ,QAAQ,KAAK,UAAU,MAAM,OAAO,cAAc,KAAK,SAAS,MAAM,MAAM;AAClF,cAAI,MAAM,SAAS,MAAM,CAAC,EAAE,MAAM,MAAM,CAAC,EAAE,YAAY,GAAG,IAAI,GAAG,EAAE,CAAC,GAAG;AACrE,wBAAY,UAAU,MAAM,GAAG,MAAM,KAAK,IAAI,MAAM,IAAI,OAAO,MAAM,CAAC,EAAE,SAAS,CAAC,IAAI,MAAM,UAAU,MAAM,KAAK,UAAU,MAAM,OAAO,cAAc,SAAS;AAAA,UACjK;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,YAAQ,QAAQ,KAAK,UAAU,MAAM,OAAO,eAAe,KAAK,SAAS,MAAM,MAAM;AACnF,kBAAY,UAAU,MAAM,GAAG,MAAM,KAAK,IAAI,OAAO,UAAU,MAAM,KAAK,UAAU,MAAM,OAAO,eAAe,SAAS;AAAA,IAC3H;AACA,YAAQ,QAAQ,KAAK,UAAU,MAAM,OAAO,UAAU,KAAK,SAAS,MAAM,MAAM;AAC9E,kBAAY,UAAU,MAAM,GAAG,MAAM,KAAK,IAAI,MAAM,IAAI,OAAO,MAAM,CAAC,EAAE,SAAS,CAAC,IAAI,MAAM,UAAU,MAAM,KAAK,UAAU,MAAM,OAAO,UAAU,SAAS;AAAA,IAC7J;AACA,QAAI,eAAe;AACnB,QAAI,WAAW;AACf,WAAO,KAAK;AACV,UAAI,CAAC,cAAc;AACjB,mBAAW;AAAA,MACb;AACA,qBAAe;AACf,UAAI;AACJ,UAAI,KAAK,QAAQ,YAAY,QAAQ,KAAK,CAAC,iBAAiB;AAC1D,YAAI,QAAQ,aAAa,KAAK,EAAE,OAAO,KAAK,GAAG,KAAK,MAAM,GAAG;AAC3D,gBAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,iBAAO,KAAK,KAAK;AACjB,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT,CAAC,GAAG;AACF;AAAA,MACF;AACA,UAAI,QAAQ,KAAK,UAAU,OAAO,GAAG,GAAG;AACtC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;AAAA,MACF;AACA,UAAI,QAAQ,KAAK,UAAU,IAAI,GAAG,GAAG;AACnC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;AAAA,MACF;AACA,UAAI,QAAQ,KAAK,UAAU,KAAK,GAAG,GAAG;AACpC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;AAAA,MACF;AACA,UAAI,QAAQ,KAAK,UAAU,QAAQ,KAAK,KAAK,OAAO,KAAK,GAAG;AAC1D,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,cAAM,YAAY,OAAO,GAAG,EAAE;AAC9B,YAAI,MAAM,SAAS,UAAU,WAAW,SAAS,QAAQ;AACvD,oBAAU,OAAO,MAAM;AACvB,oBAAU,QAAQ,MAAM;AAAA,QAC1B,OAAO;AACL,iBAAO,KAAK,KAAK;AAAA,QACnB;AACA;AAAA,MACF;AACA,UAAI,QAAQ,KAAK,UAAU,SAAS,KAAK,WAAW,QAAQ,GAAG;AAC7D,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;AAAA,MACF;AACA,UAAI,QAAQ,KAAK,UAAU,SAAS,GAAG,GAAG;AACxC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;AAAA,MACF;AACA,UAAI,QAAQ,KAAK,UAAU,GAAG,GAAG,GAAG;AAClC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;AAAA,MACF;AACA,UAAI,QAAQ,KAAK,UAAU,IAAI,GAAG,GAAG;AACnC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;AAAA,MACF;AACA,UAAI,QAAQ,KAAK,UAAU,SAAS,GAAG,GAAG;AACxC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;AAAA,MACF;AACA,UAAI,CAAC,KAAK,MAAM,WAAW,QAAQ,KAAK,UAAU,IAAI,GAAG,IAAI;AAC3D,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;AAAA,MACF;AACA,UAAI,SAAS;AACb,UAAI,KAAK,QAAQ,YAAY,aAAa;AACxC,YAAI,aAAa;AACjB,cAAM,UAAU,IAAI,MAAM,CAAC;AAC3B,YAAI;AACJ,aAAK,QAAQ,WAAW,YAAY,QAAQ,CAAC,kBAAkB;AAC7D,sBAAY,cAAc,KAAK,EAAE,OAAO,KAAK,GAAG,OAAO;AACvD,cAAI,OAAO,cAAc,YAAY,aAAa,GAAG;AACnD,yBAAa,KAAK,IAAI,YAAY,SAAS;AAAA,UAC7C;AAAA,QACF,CAAC;AACD,YAAI,aAAa,YAAY,cAAc,GAAG;AAC5C,mBAAS,IAAI,UAAU,GAAG,aAAa,CAAC;AAAA,QAC1C;AAAA,MACF;AACA,UAAI,QAAQ,KAAK,UAAU,WAAW,MAAM,GAAG;AAC7C,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,YAAI,MAAM,IAAI,MAAM,EAAE,MAAM,KAAK;AAC/B,qBAAW,MAAM,IAAI,MAAM,EAAE;AAAA,QAC/B;AACA,uBAAe;AACf,cAAM,YAAY,OAAO,GAAG,EAAE;AAC9B,YAAI,WAAW,SAAS,QAAQ;AAC9B,oBAAU,OAAO,MAAM;AACvB,oBAAU,QAAQ,MAAM;AAAA,QAC1B,OAAO;AACL,iBAAO,KAAK,KAAK;AAAA,QACnB;AACA;AAAA,MACF;AACA,UAAI,KAAK;AACP,cAAM,SAAS,4BAA4B,IAAI,WAAW,CAAC;AAC3D,YAAI,KAAK,QAAQ,QAAQ;AACvB,kBAAQ,MAAM,MAAM;AACpB;AAAA,QACF,OAAO;AACL,gBAAM,IAAI,MAAM,MAAM;AAAA,QACxB;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACF;AAGA,IAAI,YAAY,MAAM;AAAA,EACpB;AAAA,EACA;AAAA;AAAA,EAEA,YAAY,UAAU;AACpB,SAAK,UAAU,YAAY;AAAA,EAC7B;AAAA,EACA,MAAM,OAAO;AACX,WAAO;AAAA,EACT;AAAA,EACA,KAAK,EAAE,MAAM,MAAM,QAAQ,GAAG;AAC5B,UAAM,cAAc,QAAQ,IAAI,MAAM,MAAM,aAAa,IAAI,CAAC;AAC9D,UAAM,OAAO,KAAK,QAAQ,MAAM,eAAe,EAAE,IAAI;AACrD,QAAI,CAAC,YAAY;AACf,aAAO,iBAAiB,UAAU,OAAO,QAAQ,MAAM,IAAI,KAAK;AAAA,IAClE;AACA,WAAO,gCAAgC,QAAQ,UAAU,IAAI,QAAQ,UAAU,OAAO,QAAQ,MAAM,IAAI,KAAK;AAAA,EAC/G;AAAA,EACA,WAAW,EAAE,OAAO,GAAG;AACrB,UAAM,OAAO,KAAK,OAAO,MAAM,MAAM;AACrC,WAAO;AAAA,EACT,IAAI;AAAA;AAAA,EAEJ;AAAA,EACA,KAAK,EAAE,KAAK,GAAG;AACb,WAAO;AAAA,EACT;AAAA,EACA,QAAQ,EAAE,QAAQ,MAAM,GAAG;AACzB,WAAO,KAAK,KAAK,IAAI,KAAK,OAAO,YAAY,MAAM,CAAC,MAAM,KAAK;AAAA;AAAA,EAEjE;AAAA,EACA,GAAG,OAAO;AACR,WAAO;AAAA,EACT;AAAA,EACA,KAAK,OAAO;AACV,UAAM,UAAU,MAAM;AACtB,UAAM,QAAQ,MAAM;AACpB,QAAI,OAAO;AACX,aAAS,IAAI,GAAG,IAAI,MAAM,MAAM,QAAQ,KAAK;AAC3C,YAAM,OAAO,MAAM,MAAM,CAAC;AAC1B,cAAQ,KAAK,SAAS,IAAI;AAAA,IAC5B;AACA,UAAM,OAAO,UAAU,OAAO;AAC9B,UAAM,YAAY,WAAW,UAAU,IAAI,aAAa,QAAQ,MAAM;AACtE,WAAO,MAAM,OAAO,YAAY,QAAQ,OAAO,OAAO,OAAO;AAAA,EAC/D;AAAA,EACA,SAAS,MAAM;AACb,QAAI,WAAW;AACf,QAAI,KAAK,MAAM;AACb,YAAM,WAAW,KAAK,SAAS,EAAE,SAAS,CAAC,CAAC,KAAK,QAAQ,CAAC;AAC1D,UAAI,KAAK,OAAO;AACd,YAAI,KAAK,OAAO,CAAC,GAAG,SAAS,aAAa;AACxC,eAAK,OAAO,CAAC,EAAE,OAAO,WAAW,MAAM,KAAK,OAAO,CAAC,EAAE;AACtD,cAAI,KAAK,OAAO,CAAC,EAAE,UAAU,KAAK,OAAO,CAAC,EAAE,OAAO,SAAS,KAAK,KAAK,OAAO,CAAC,EAAE,OAAO,CAAC,EAAE,SAAS,QAAQ;AACzG,iBAAK,OAAO,CAAC,EAAE,OAAO,CAAC,EAAE,OAAO,WAAW,MAAM,QAAQ,KAAK,OAAO,CAAC,EAAE,OAAO,CAAC,EAAE,IAAI;AACtF,iBAAK,OAAO,CAAC,EAAE,OAAO,CAAC,EAAE,UAAU;AAAA,UACrC;AAAA,QACF,OAAO;AACL,eAAK,OAAO,QAAQ;AAAA,YAClB,MAAM;AAAA,YACN,KAAK,WAAW;AAAA,YAChB,MAAM,WAAW;AAAA,YACjB,SAAS;AAAA,UACX,CAAC;AAAA,QACH;AAAA,MACF,OAAO;AACL,oBAAY,WAAW;AAAA,MACzB;AAAA,IACF;AACA,gBAAY,KAAK,OAAO,MAAM,KAAK,QAAQ,CAAC,CAAC,KAAK,KAAK;AACvD,WAAO,OAAO,QAAQ;AAAA;AAAA,EAExB;AAAA,EACA,SAAS,EAAE,QAAQ,GAAG;AACpB,WAAO,aAAa,UAAU,gBAAgB,MAAM;AAAA,EACtD;AAAA,EACA,UAAU,EAAE,OAAO,GAAG;AACpB,WAAO,MAAM,KAAK,OAAO,YAAY,MAAM,CAAC;AAAA;AAAA,EAE9C;AAAA,EACA,MAAM,OAAO;AACX,QAAI,SAAS;AACb,QAAI,OAAO;AACX,aAAS,IAAI,GAAG,IAAI,MAAM,OAAO,QAAQ,KAAK;AAC5C,cAAQ,KAAK,UAAU,MAAM,OAAO,CAAC,CAAC;AAAA,IACxC;AACA,cAAU,KAAK,SAAS,EAAE,MAAM,KAAK,CAAC;AACtC,QAAI,OAAO;AACX,aAAS,IAAI,GAAG,IAAI,MAAM,KAAK,QAAQ,KAAK;AAC1C,YAAM,MAAM,MAAM,KAAK,CAAC;AACxB,aAAO;AACP,eAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,gBAAQ,KAAK,UAAU,IAAI,CAAC,CAAC;AAAA,MAC/B;AACA,cAAQ,KAAK,SAAS,EAAE,MAAM,KAAK,CAAC;AAAA,IACtC;AACA,QAAI,KAAM,QAAO,UAAU,IAAI;AAC/B,WAAO,uBAAuB,SAAS,eAAe,OAAO;AAAA,EAC/D;AAAA,EACA,SAAS,EAAE,KAAK,GAAG;AACjB,WAAO;AAAA,EACT,IAAI;AAAA;AAAA,EAEJ;AAAA,EACA,UAAU,OAAO;AACf,UAAM,UAAU,KAAK,OAAO,YAAY,MAAM,MAAM;AACpD,UAAM,OAAO,MAAM,SAAS,OAAO;AACnC,UAAM,OAAO,MAAM,QAAQ,IAAI,IAAI,WAAW,MAAM,KAAK,OAAO,IAAI,IAAI;AACxE,WAAO,OAAO,UAAU,KAAK,IAAI;AAAA;AAAA,EAEnC;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,EAAE,OAAO,GAAG;AACjB,WAAO,WAAW,KAAK,OAAO,YAAY,MAAM,CAAC;AAAA,EACnD;AAAA,EACA,GAAG,EAAE,OAAO,GAAG;AACb,WAAO,OAAO,KAAK,OAAO,YAAY,MAAM,CAAC;AAAA,EAC/C;AAAA,EACA,SAAS,EAAE,KAAK,GAAG;AACjB,WAAO,SAAS,QAAQ,MAAM,IAAI,CAAC;AAAA,EACrC;AAAA,EACA,GAAG,OAAO;AACR,WAAO;AAAA,EACT;AAAA,EACA,IAAI,EAAE,OAAO,GAAG;AACd,WAAO,QAAQ,KAAK,OAAO,YAAY,MAAM,CAAC;AAAA,EAChD;AAAA,EACA,KAAK,EAAE,MAAM,OAAO,OAAO,GAAG;AAC5B,UAAM,OAAO,KAAK,OAAO,YAAY,MAAM;AAC3C,UAAM,YAAY,SAAS,IAAI;AAC/B,QAAI,cAAc,MAAM;AACtB,aAAO;AAAA,IACT;AACA,WAAO;AACP,QAAI,MAAM,cAAc,OAAO;AAC/B,QAAI,OAAO;AACT,aAAO,aAAa,QAAQ,KAAK,IAAI;AAAA,IACvC;AACA,WAAO,MAAM,OAAO;AACpB,WAAO;AAAA,EACT;AAAA,EACA,MAAM,EAAE,MAAM,OAAO,MAAM,OAAO,GAAG;AACnC,QAAI,QAAQ;AACV,aAAO,KAAK,OAAO,YAAY,QAAQ,KAAK,OAAO,YAAY;AAAA,IACjE;AACA,UAAM,YAAY,SAAS,IAAI;AAC/B,QAAI,cAAc,MAAM;AACtB,aAAO,QAAQ,IAAI;AAAA,IACrB;AACA,WAAO;AACP,QAAI,MAAM,aAAa,IAAI,UAAU,IAAI;AACzC,QAAI,OAAO;AACT,aAAO,WAAW,QAAQ,KAAK,CAAC;AAAA,IAClC;AACA,WAAO;AACP,WAAO;AAAA,EACT;AAAA,EACA,KAAK,OAAO;AACV,WAAO,YAAY,SAAS,MAAM,SAAS,KAAK,OAAO,YAAY,MAAM,MAAM,IAAI,aAAa,SAAS,MAAM,UAAU,MAAM,OAAO,QAAQ,MAAM,IAAI;AAAA,EAC1J;AACF;AAGA,IAAI,gBAAgB,MAAM;AAAA;AAAA,EAExB,OAAO,EAAE,KAAK,GAAG;AACf,WAAO;AAAA,EACT;AAAA,EACA,GAAG,EAAE,KAAK,GAAG;AACX,WAAO;AAAA,EACT;AAAA,EACA,SAAS,EAAE,KAAK,GAAG;AACjB,WAAO;AAAA,EACT;AAAA,EACA,IAAI,EAAE,KAAK,GAAG;AACZ,WAAO;AAAA,EACT;AAAA,EACA,KAAK,EAAE,KAAK,GAAG;AACb,WAAO;AAAA,EACT;AAAA,EACA,KAAK,EAAE,KAAK,GAAG;AACb,WAAO;AAAA,EACT;AAAA,EACA,KAAK,EAAE,KAAK,GAAG;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,MAAM,EAAE,KAAK,GAAG;AACd,WAAO,KAAK;AAAA,EACd;AAAA,EACA,KAAK;AACH,WAAO;AAAA,EACT;AACF;AAGA,IAAI,UAAU,MAAM,SAAS;AAAA,EAC3B;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAY,UAAU;AACpB,SAAK,UAAU,YAAY;AAC3B,SAAK,QAAQ,WAAW,KAAK,QAAQ,YAAY,IAAI,UAAU;AAC/D,SAAK,WAAW,KAAK,QAAQ;AAC7B,SAAK,SAAS,UAAU,KAAK;AAC7B,SAAK,SAAS,SAAS;AACvB,SAAK,eAAe,IAAI,cAAc;AAAA,EACxC;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,MAAM,QAAQ,UAAU;AAC7B,UAAM,UAAU,IAAI,SAAS,QAAQ;AACrC,WAAO,QAAQ,MAAM,MAAM;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,YAAY,QAAQ,UAAU;AACnC,UAAM,UAAU,IAAI,SAAS,QAAQ;AACrC,WAAO,QAAQ,YAAY,MAAM;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA,EAIA,MAAM,QAAQ,MAAM,MAAM;AACxB,QAAI,MAAM;AACV,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,YAAM,WAAW,OAAO,CAAC;AACzB,UAAI,KAAK,QAAQ,YAAY,YAAY,SAAS,IAAI,GAAG;AACvD,cAAM,eAAe;AACrB,cAAM,MAAM,KAAK,QAAQ,WAAW,UAAU,aAAa,IAAI,EAAE,KAAK,EAAE,QAAQ,KAAK,GAAG,YAAY;AACpG,YAAI,QAAQ,SAAS,CAAC,CAAC,SAAS,MAAM,WAAW,QAAQ,SAAS,cAAc,QAAQ,QAAQ,aAAa,MAAM,EAAE,SAAS,aAAa,IAAI,GAAG;AAChJ,iBAAO,OAAO;AACd;AAAA,QACF;AAAA,MACF;AACA,YAAM,QAAQ;AACd,cAAQ,MAAM,MAAM;AAAA,QAClB,KAAK,SAAS;AACZ,iBAAO,KAAK,SAAS,MAAM,KAAK;AAChC;AAAA,QACF;AAAA,QACA,KAAK,MAAM;AACT,iBAAO,KAAK,SAAS,GAAG,KAAK;AAC7B;AAAA,QACF;AAAA,QACA,KAAK,WAAW;AACd,iBAAO,KAAK,SAAS,QAAQ,KAAK;AAClC;AAAA,QACF;AAAA,QACA,KAAK,QAAQ;AACX,iBAAO,KAAK,SAAS,KAAK,KAAK;AAC/B;AAAA,QACF;AAAA,QACA,KAAK,SAAS;AACZ,iBAAO,KAAK,SAAS,MAAM,KAAK;AAChC;AAAA,QACF;AAAA,QACA,KAAK,cAAc;AACjB,iBAAO,KAAK,SAAS,WAAW,KAAK;AACrC;AAAA,QACF;AAAA,QACA,KAAK,QAAQ;AACX,iBAAO,KAAK,SAAS,KAAK,KAAK;AAC/B;AAAA,QACF;AAAA,QACA,KAAK,QAAQ;AACX,iBAAO,KAAK,SAAS,KAAK,KAAK;AAC/B;AAAA,QACF;AAAA,QACA,KAAK,aAAa;AAChB,iBAAO,KAAK,SAAS,UAAU,KAAK;AACpC;AAAA,QACF;AAAA,QACA,KAAK,QAAQ;AACX,cAAI,YAAY;AAChB,cAAI,OAAO,KAAK,SAAS,KAAK,SAAS;AACvC,iBAAO,IAAI,IAAI,OAAO,UAAU,OAAO,IAAI,CAAC,EAAE,SAAS,QAAQ;AAC7D,wBAAY,OAAO,EAAE,CAAC;AACtB,oBAAQ,OAAO,KAAK,SAAS,KAAK,SAAS;AAAA,UAC7C;AACA,cAAI,KAAK;AACP,mBAAO,KAAK,SAAS,UAAU;AAAA,cAC7B,MAAM;AAAA,cACN,KAAK;AAAA,cACL,MAAM;AAAA,cACN,QAAQ,CAAC,EAAE,MAAM,QAAQ,KAAK,MAAM,MAAM,MAAM,SAAS,KAAK,CAAC;AAAA,YACjE,CAAC;AAAA,UACH,OAAO;AACL,mBAAO;AAAA,UACT;AACA;AAAA,QACF;AAAA,QACA,SAAS;AACP,gBAAM,SAAS,iBAAiB,MAAM,OAAO;AAC7C,cAAI,KAAK,QAAQ,QAAQ;AACvB,oBAAQ,MAAM,MAAM;AACpB,mBAAO;AAAA,UACT,OAAO;AACL,kBAAM,IAAI,MAAM,MAAM;AAAA,UACxB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAIA,YAAY,QAAQ,WAAW,KAAK,UAAU;AAC5C,QAAI,MAAM;AACV,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,YAAM,WAAW,OAAO,CAAC;AACzB,UAAI,KAAK,QAAQ,YAAY,YAAY,SAAS,IAAI,GAAG;AACvD,cAAM,MAAM,KAAK,QAAQ,WAAW,UAAU,SAAS,IAAI,EAAE,KAAK,EAAE,QAAQ,KAAK,GAAG,QAAQ;AAC5F,YAAI,QAAQ,SAAS,CAAC,CAAC,UAAU,QAAQ,QAAQ,SAAS,UAAU,MAAM,YAAY,MAAM,OAAO,MAAM,EAAE,SAAS,SAAS,IAAI,GAAG;AAClI,iBAAO,OAAO;AACd;AAAA,QACF;AAAA,MACF;AACA,YAAM,QAAQ;AACd,cAAQ,MAAM,MAAM;AAAA,QAClB,KAAK,UAAU;AACb,iBAAO,SAAS,KAAK,KAAK;AAC1B;AAAA,QACF;AAAA,QACA,KAAK,QAAQ;AACX,iBAAO,SAAS,KAAK,KAAK;AAC1B;AAAA,QACF;AAAA,QACA,KAAK,QAAQ;AACX,iBAAO,SAAS,KAAK,KAAK;AAC1B;AAAA,QACF;AAAA,QACA,KAAK,SAAS;AACZ,iBAAO,SAAS,MAAM,KAAK;AAC3B;AAAA,QACF;AAAA,QACA,KAAK,UAAU;AACb,iBAAO,SAAS,OAAO,KAAK;AAC5B;AAAA,QACF;AAAA,QACA,KAAK,MAAM;AACT,iBAAO,SAAS,GAAG,KAAK;AACxB;AAAA,QACF;AAAA,QACA,KAAK,YAAY;AACf,iBAAO,SAAS,SAAS,KAAK;AAC9B;AAAA,QACF;AAAA,QACA,KAAK,MAAM;AACT,iBAAO,SAAS,GAAG,KAAK;AACxB;AAAA,QACF;AAAA,QACA,KAAK,OAAO;AACV,iBAAO,SAAS,IAAI,KAAK;AACzB;AAAA,QACF;AAAA,QACA,KAAK,QAAQ;AACX,iBAAO,SAAS,KAAK,KAAK;AAC1B;AAAA,QACF;AAAA,QACA,SAAS;AACP,gBAAM,SAAS,iBAAiB,MAAM,OAAO;AAC7C,cAAI,KAAK,QAAQ,QAAQ;AACvB,oBAAQ,MAAM,MAAM;AACpB,mBAAO;AAAA,UACT,OAAO;AACL,kBAAM,IAAI,MAAM,MAAM;AAAA,UACxB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACF;AAGA,IAAI,SAAS,MAAM;AAAA,EACjB;AAAA,EACA;AAAA,EACA,YAAY,UAAU;AACpB,SAAK,UAAU,YAAY;AAAA,EAC7B;AAAA,EACA,OAAO,mBAAmC,oBAAI,IAAI;AAAA,IAChD;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AAAA;AAAA;AAAA;AAAA,EAID,WAAW,UAAU;AACnB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAIA,YAAY,OAAO;AACjB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAIA,iBAAiB,QAAQ;AACvB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAIA,eAAe;AACb,WAAO,KAAK,QAAQ,OAAO,MAAM,OAAO;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA,EAIA,gBAAgB;AACd,WAAO,KAAK,QAAQ,QAAQ,QAAQ,QAAQ;AAAA,EAC9C;AACF;AAGA,IAAI,SAAS,MAAM;AAAA,EACjB,WAAW,aAAa;AAAA,EACxB,UAAU,KAAK;AAAA,EACf,QAAQ,KAAK,cAAc,IAAI;AAAA,EAC/B,cAAc,KAAK,cAAc,KAAK;AAAA,EACtC,SAAS;AAAA,EACT,WAAW;AAAA,EACX,eAAe;AAAA,EACf,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,QAAQ;AAAA,EACR,eAAe,MAAM;AACnB,SAAK,IAAI,GAAG,IAAI;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW,QAAQ,UAAU;AAC3B,QAAI,SAAS,CAAC;AACd,eAAW,SAAS,QAAQ;AAC1B,eAAS,OAAO,OAAO,SAAS,KAAK,MAAM,KAAK,CAAC;AACjD,cAAQ,MAAM,MAAM;AAAA,QAClB,KAAK,SAAS;AACZ,gBAAM,aAAa;AACnB,qBAAW,QAAQ,WAAW,QAAQ;AACpC,qBAAS,OAAO,OAAO,KAAK,WAAW,KAAK,QAAQ,QAAQ,CAAC;AAAA,UAC/D;AACA,qBAAW,OAAO,WAAW,MAAM;AACjC,uBAAW,QAAQ,KAAK;AACtB,uBAAS,OAAO,OAAO,KAAK,WAAW,KAAK,QAAQ,QAAQ,CAAC;AAAA,YAC/D;AAAA,UACF;AACA;AAAA,QACF;AAAA,QACA,KAAK,QAAQ;AACX,gBAAM,YAAY;AAClB,mBAAS,OAAO,OAAO,KAAK,WAAW,UAAU,OAAO,QAAQ,CAAC;AACjE;AAAA,QACF;AAAA,QACA,SAAS;AACP,gBAAM,eAAe;AACrB,cAAI,KAAK,SAAS,YAAY,cAAc,aAAa,IAAI,GAAG;AAC9D,iBAAK,SAAS,WAAW,YAAY,aAAa,IAAI,EAAE,QAAQ,CAAC,gBAAgB;AAC/E,oBAAM,UAAU,aAAa,WAAW,EAAE,KAAK,QAAQ;AACvD,uBAAS,OAAO,OAAO,KAAK,WAAW,SAAS,QAAQ,CAAC;AAAA,YAC3D,CAAC;AAAA,UACH,WAAW,aAAa,QAAQ;AAC9B,qBAAS,OAAO,OAAO,KAAK,WAAW,aAAa,QAAQ,QAAQ,CAAC;AAAA,UACvE;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,OAAO,MAAM;AACX,UAAM,aAAa,KAAK,SAAS,cAAc,EAAE,WAAW,CAAC,GAAG,aAAa,CAAC,EAAE;AAChF,SAAK,QAAQ,CAAC,SAAS;AACrB,YAAM,OAAO,mBAAK;AAClB,WAAK,QAAQ,KAAK,SAAS,SAAS,KAAK,SAAS;AAClD,UAAI,KAAK,YAAY;AACnB,aAAK,WAAW,QAAQ,CAAC,QAAQ;AAC/B,cAAI,CAAC,IAAI,MAAM;AACb,kBAAM,IAAI,MAAM,yBAAyB;AAAA,UAC3C;AACA,cAAI,cAAc,KAAK;AACrB,kBAAM,eAAe,WAAW,UAAU,IAAI,IAAI;AAClD,gBAAI,cAAc;AAChB,yBAAW,UAAU,IAAI,IAAI,IAAI,YAAY,OAAO;AAClD,oBAAI,MAAM,IAAI,SAAS,MAAM,MAAM,KAAK;AACxC,oBAAI,QAAQ,OAAO;AACjB,wBAAM,aAAa,MAAM,MAAM,KAAK;AAAA,gBACtC;AACA,uBAAO;AAAA,cACT;AAAA,YACF,OAAO;AACL,yBAAW,UAAU,IAAI,IAAI,IAAI,IAAI;AAAA,YACvC;AAAA,UACF;AACA,cAAI,eAAe,KAAK;AACtB,gBAAI,CAAC,IAAI,SAAS,IAAI,UAAU,WAAW,IAAI,UAAU,UAAU;AACjE,oBAAM,IAAI,MAAM,6CAA6C;AAAA,YAC/D;AACA,kBAAM,WAAW,WAAW,IAAI,KAAK;AACrC,gBAAI,UAAU;AACZ,uBAAS,QAAQ,IAAI,SAAS;AAAA,YAChC,OAAO;AACL,yBAAW,IAAI,KAAK,IAAI,CAAC,IAAI,SAAS;AAAA,YACxC;AACA,gBAAI,IAAI,OAAO;AACb,kBAAI,IAAI,UAAU,SAAS;AACzB,oBAAI,WAAW,YAAY;AACzB,6BAAW,WAAW,KAAK,IAAI,KAAK;AAAA,gBACtC,OAAO;AACL,6BAAW,aAAa,CAAC,IAAI,KAAK;AAAA,gBACpC;AAAA,cACF,WAAW,IAAI,UAAU,UAAU;AACjC,oBAAI,WAAW,aAAa;AAC1B,6BAAW,YAAY,KAAK,IAAI,KAAK;AAAA,gBACvC,OAAO;AACL,6BAAW,cAAc,CAAC,IAAI,KAAK;AAAA,gBACrC;AAAA,cACF;AAAA,YACF;AAAA,UACF;AACA,cAAI,iBAAiB,OAAO,IAAI,aAAa;AAC3C,uBAAW,YAAY,IAAI,IAAI,IAAI,IAAI;AAAA,UACzC;AAAA,QACF,CAAC;AACD,aAAK,aAAa;AAAA,MACpB;AACA,UAAI,KAAK,UAAU;AACjB,cAAM,WAAW,KAAK,SAAS,YAAY,IAAI,UAAU,KAAK,QAAQ;AACtE,mBAAW,QAAQ,KAAK,UAAU;AAChC,cAAI,EAAE,QAAQ,WAAW;AACvB,kBAAM,IAAI,MAAM,aAAa,IAAI,kBAAkB;AAAA,UACrD;AACA,cAAI,CAAC,WAAW,QAAQ,EAAE,SAAS,IAAI,GAAG;AACxC;AAAA,UACF;AACA,gBAAM,eAAe;AACrB,gBAAM,eAAe,KAAK,SAAS,YAAY;AAC/C,gBAAM,eAAe,SAAS,YAAY;AAC1C,mBAAS,YAAY,IAAI,IAAI,UAAU;AACrC,gBAAI,MAAM,aAAa,MAAM,UAAU,KAAK;AAC5C,gBAAI,QAAQ,OAAO;AACjB,oBAAM,aAAa,MAAM,UAAU,KAAK;AAAA,YAC1C;AACA,mBAAO,OAAO;AAAA,UAChB;AAAA,QACF;AACA,aAAK,WAAW;AAAA,MAClB;AACA,UAAI,KAAK,WAAW;AAClB,cAAM,YAAY,KAAK,SAAS,aAAa,IAAI,WAAW,KAAK,QAAQ;AACzE,mBAAW,QAAQ,KAAK,WAAW;AACjC,cAAI,EAAE,QAAQ,YAAY;AACxB,kBAAM,IAAI,MAAM,cAAc,IAAI,kBAAkB;AAAA,UACtD;AACA,cAAI,CAAC,WAAW,SAAS,OAAO,EAAE,SAAS,IAAI,GAAG;AAChD;AAAA,UACF;AACA,gBAAM,gBAAgB;AACtB,gBAAM,gBAAgB,KAAK,UAAU,aAAa;AAClD,gBAAM,gBAAgB,UAAU,aAAa;AAC7C,oBAAU,aAAa,IAAI,IAAI,UAAU;AACvC,gBAAI,MAAM,cAAc,MAAM,WAAW,KAAK;AAC9C,gBAAI,QAAQ,OAAO;AACjB,oBAAM,cAAc,MAAM,WAAW,KAAK;AAAA,YAC5C;AACA,mBAAO;AAAA,UACT;AAAA,QACF;AACA,aAAK,YAAY;AAAA,MACnB;AACA,UAAI,KAAK,OAAO;AACd,cAAM,QAAQ,KAAK,SAAS,SAAS,IAAI,OAAO;AAChD,mBAAW,QAAQ,KAAK,OAAO;AAC7B,cAAI,EAAE,QAAQ,QAAQ;AACpB,kBAAM,IAAI,MAAM,SAAS,IAAI,kBAAkB;AAAA,UACjD;AACA,cAAI,CAAC,WAAW,OAAO,EAAE,SAAS,IAAI,GAAG;AACvC;AAAA,UACF;AACA,gBAAM,YAAY;AAClB,gBAAM,YAAY,KAAK,MAAM,SAAS;AACtC,gBAAM,WAAW,MAAM,SAAS;AAChC,cAAI,OAAO,iBAAiB,IAAI,IAAI,GAAG;AACrC,kBAAM,SAAS,IAAI,CAAC,QAAQ;AAC1B,kBAAI,KAAK,SAAS,OAAO;AACvB,uBAAO,QAAQ,QAAQ,UAAU,KAAK,OAAO,GAAG,CAAC,EAAE,KAAK,CAAC,SAAS;AAChE,yBAAO,SAAS,KAAK,OAAO,IAAI;AAAA,gBAClC,CAAC;AAAA,cACH;AACA,oBAAM,MAAM,UAAU,KAAK,OAAO,GAAG;AACrC,qBAAO,SAAS,KAAK,OAAO,GAAG;AAAA,YACjC;AAAA,UACF,OAAO;AACL,kBAAM,SAAS,IAAI,IAAI,UAAU;AAC/B,kBAAI,MAAM,UAAU,MAAM,OAAO,KAAK;AACtC,kBAAI,QAAQ,OAAO;AACjB,sBAAM,SAAS,MAAM,OAAO,KAAK;AAAA,cACnC;AACA,qBAAO;AAAA,YACT;AAAA,UACF;AAAA,QACF;AACA,aAAK,QAAQ;AAAA,MACf;AACA,UAAI,KAAK,YAAY;AACnB,cAAM,cAAc,KAAK,SAAS;AAClC,cAAM,iBAAiB,KAAK;AAC5B,aAAK,aAAa,SAAS,OAAO;AAChC,cAAI,SAAS,CAAC;AACd,iBAAO,KAAK,eAAe,KAAK,MAAM,KAAK,CAAC;AAC5C,cAAI,aAAa;AACf,qBAAS,OAAO,OAAO,YAAY,KAAK,MAAM,KAAK,CAAC;AAAA,UACtD;AACA,iBAAO;AAAA,QACT;AAAA,MACF;AACA,WAAK,WAAW,kCAAK,KAAK,WAAa;AAAA,IACzC,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EACA,WAAW,KAAK;AACd,SAAK,WAAW,kCAAK,KAAK,WAAa;AACvC,WAAO;AAAA,EACT;AAAA,EACA,MAAM,KAAK,UAAU;AACnB,WAAO,OAAO,IAAI,KAAK,YAAY,KAAK,QAAQ;AAAA,EAClD;AAAA,EACA,OAAO,QAAQ,UAAU;AACvB,WAAO,QAAQ,MAAM,QAAQ,YAAY,KAAK,QAAQ;AAAA,EACxD;AAAA,EACA,cAAc,WAAW;AACvB,UAAM,SAAS,CAAC,KAAK,aAAa;AAChC,YAAM,UAAU,mBAAK;AACrB,YAAM,MAAM,kCAAK,KAAK,WAAa;AACnC,YAAM,aAAa,KAAK,QAAQ,CAAC,CAAC,IAAI,QAAQ,CAAC,CAAC,IAAI,KAAK;AACzD,UAAI,KAAK,SAAS,UAAU,QAAQ,QAAQ,UAAU,OAAO;AAC3D,eAAO,WAAW,IAAI,MAAM,oIAAoI,CAAC;AAAA,MACnK;AACA,UAAI,OAAO,QAAQ,eAAe,QAAQ,MAAM;AAC9C,eAAO,WAAW,IAAI,MAAM,gDAAgD,CAAC;AAAA,MAC/E;AACA,UAAI,OAAO,QAAQ,UAAU;AAC3B,eAAO,WAAW,IAAI,MAAM,0CAA0C,OAAO,UAAU,SAAS,KAAK,GAAG,IAAI,mBAAmB,CAAC;AAAA,MAClI;AACA,UAAI,IAAI,OAAO;AACb,YAAI,MAAM,UAAU;AACpB,YAAI,MAAM,QAAQ;AAAA,MACpB;AACA,YAAM,SAAS,IAAI,QAAQ,IAAI,MAAM,aAAa,IAAI,YAAY,OAAO,MAAM,OAAO;AACtF,YAAM,UAAU,IAAI,QAAQ,IAAI,MAAM,cAAc,IAAI,YAAY,QAAQ,QAAQ,QAAQ;AAC5F,UAAI,IAAI,OAAO;AACb,eAAO,QAAQ,QAAQ,IAAI,QAAQ,IAAI,MAAM,WAAW,GAAG,IAAI,GAAG,EAAE,KAAK,CAAC,SAAS,OAAO,MAAM,GAAG,CAAC,EAAE,KAAK,CAAC,WAAW,IAAI,QAAQ,IAAI,MAAM,iBAAiB,MAAM,IAAI,MAAM,EAAE,KAAK,CAAC,WAAW,IAAI,aAAa,QAAQ,IAAI,KAAK,WAAW,QAAQ,IAAI,UAAU,CAAC,EAAE,KAAK,MAAM,MAAM,IAAI,MAAM,EAAE,KAAK,CAAC,WAAW,QAAQ,QAAQ,GAAG,CAAC,EAAE,KAAK,CAAC,UAAU,IAAI,QAAQ,IAAI,MAAM,YAAY,KAAK,IAAI,KAAK,EAAE,MAAM,UAAU;AAAA,MAC/Z;AACA,UAAI;AACF,YAAI,IAAI,OAAO;AACb,gBAAM,IAAI,MAAM,WAAW,GAAG;AAAA,QAChC;AACA,YAAI,SAAS,OAAO,KAAK,GAAG;AAC5B,YAAI,IAAI,OAAO;AACb,mBAAS,IAAI,MAAM,iBAAiB,MAAM;AAAA,QAC5C;AACA,YAAI,IAAI,YAAY;AAClB,eAAK,WAAW,QAAQ,IAAI,UAAU;AAAA,QACxC;AACA,YAAI,QAAQ,QAAQ,QAAQ,GAAG;AAC/B,YAAI,IAAI,OAAO;AACb,kBAAQ,IAAI,MAAM,YAAY,KAAK;AAAA,QACrC;AACA,eAAO;AAAA,MACT,SAAS,GAAG;AACV,eAAO,WAAW,CAAC;AAAA,MACrB;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,QAAQ,QAAQ,OAAO;AACrB,WAAO,CAAC,MAAM;AACZ,QAAE,WAAW;AACb,UAAI,QAAQ;AACV,cAAM,MAAM,mCAAmC,QAAQ,EAAE,UAAU,IAAI,IAAI,IAAI;AAC/E,YAAI,OAAO;AACT,iBAAO,QAAQ,QAAQ,GAAG;AAAA,QAC5B;AACA,eAAO;AAAA,MACT;AACA,UAAI,OAAO;AACT,eAAO,QAAQ,OAAO,CAAC;AAAA,MACzB;AACA,YAAM;AAAA,IACR;AAAA,EACF;AACF;AAGA,IAAI,iBAAiB,IAAI,OAAO;AAChC,SAAS,OAAO,KAAK,KAAK;AACxB,SAAO,eAAe,MAAM,KAAK,GAAG;AACtC;AACA,OAAO,UAAU,OAAO,aAAa,SAAS,UAAU;AACtD,iBAAe,WAAW,QAAQ;AAClC,SAAO,WAAW,eAAe;AACjC,iBAAe,OAAO,QAAQ;AAC9B,SAAO;AACT;AACA,OAAO,cAAc;AACrB,OAAO,WAAW;AAClB,OAAO,MAAM,YAAY,MAAM;AAC7B,iBAAe,IAAI,GAAG,IAAI;AAC1B,SAAO,WAAW,eAAe;AACjC,iBAAe,OAAO,QAAQ;AAC9B,SAAO;AACT;AACA,OAAO,aAAa,SAAS,QAAQ,UAAU;AAC7C,SAAO,eAAe,WAAW,QAAQ,QAAQ;AACnD;AACA,OAAO,cAAc,eAAe;AACpC,OAAO,SAAS;AAChB,OAAO,SAAS,QAAQ;AACxB,OAAO,WAAW;AAClB,OAAO,eAAe;AACtB,OAAO,QAAQ;AACf,OAAO,QAAQ,OAAO;AACtB,OAAO,YAAY;AACnB,OAAO,QAAQ;AACf,OAAO,QAAQ;AACf,IAAI,UAAU,OAAO;AACrB,IAAI,aAAa,OAAO;AACxB,IAAI,MAAM,OAAO;AACjB,IAAI,aAAa,OAAO;AACxB,IAAI,cAAc,OAAO;AAEzB,IAAI,SAAS,QAAQ;AACrB,IAAI,QAAQ,OAAO;;;AChnEnB,IAAM,MAAM,CAAC,GAAG;AAIhB,IAAM,mBAAmB;AACzB,IAAM,qBAAqB;AAC3B,IAAM,4BAAN,MAAM,0BAAyB;AAAA,EAC7B,cAAc;AACZ,SAAK,gBAAgB,IAAI,QAAQ;AACjC,SAAK,SAAS,SAAS,KAAK,cAAc,KAAK,UAAU,MAAM,MAAM,GAAG,IAAI,GAAG,MAAM,GAAI,EAAE,KAAK,MAAM,KAAK,CAAC,CAAC,CAAC,GAAG,qBAAqB,GAAG,YAAY,CAAC,CAAC,CAAC;AACxJ,SAAK,aAAa,SAAS,MAAM,KAAK,OAAO,IAAI,qBAAqB,gBAAgB;AAAA,EACxF;AAAA,EACA,yBAAyB;AACvB,SAAK,cAAc,KAAK;AAAA,EAC1B;AAgCF;AA9BI,0BAAK,OAAO,SAAS,iCAAiC,mBAAmB;AACvE,SAAO,KAAK,qBAAqB,2BAA0B;AAC7D;AAGA,0BAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,oBAAoB,CAAC;AAAA,EAClC,OAAO;AAAA,EACP,MAAM;AAAA,EACN,QAAQ,CAAC,CAAC,GAAG,6BAA6B,GAAG,OAAO,CAAC;AAAA,EACrD,UAAU,SAAS,kCAAkC,IAAI,KAAK;AAC5D,QAAI,KAAK,GAAG;AACV,MAAG,eAAe,GAAG,UAAU,CAAC;AAChC,MAAG,WAAW,SAAS,SAAS,4DAA4D;AAC1F,eAAO,IAAI,uBAAuB;AAAA,MACpC,CAAC;AACD,MAAG,OAAO,CAAC;AACX,MAAG,aAAa;AAAA,IAClB;AACA,QAAI,KAAK,GAAG;AACV,MAAG,YAAY,UAAU,IAAI,OAAO,CAAC;AACrC,MAAG,UAAU;AACb,MAAG,kBAAkB,IAAI,WAAW,CAAC;AAAA,IACvC;AAAA,EACF;AAAA,EACA,eAAe;AAAA,EACf,iBAAiB;AACnB,CAAC;AAtCL,IAAM,2BAAN;AAAA,CAyCC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,0BAA0B,CAAC;AAAA,IACjG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOV,iBAAiB,wBAAwB;AAAA,IAC3C,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,oBAAoB,IAAI,eAAe,mBAAmB;AAGhE,IAAM,uBAAN,MAA2B;AAAC;AAC5B,IAAM,gBAAN,MAAM,cAAa;AAAA,EACjB,UAAU,OAAO,UAAU;AACzB,QAAI,SAAS,MAAM;AACjB,cAAQ;AAAA,IACV;AACA,QAAI,YAAY,MAAM;AACpB,iBAAW;AAAA,IACb;AACA,QAAI,OAAO,UAAU,UAAU;AAC7B,cAAQ,MAAM,6DAA6D,OAAO,KAAK,GAAG;AAC1F,aAAO;AAAA,IACT;AACA,QAAI,OAAO,aAAa,UAAU;AAChC,cAAQ,MAAM,4DAA4D,OAAO,QAAQ,GAAG;AAC5F,aAAO;AAAA,IACT;AACA,WAAO,QAAQ,WAAW,OAAO,QAAQ;AAAA,EAC3C;AAaF;AAXI,cAAK,OAAO,SAAS,qBAAqB,mBAAmB;AAC3D,SAAO,KAAK,qBAAqB,eAAc;AACjD;AAGA,cAAK,QAA0B,aAAa;AAAA,EAC1C,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AACR,CAAC;AA5BL,IAAM,eAAN;AAAA,CA+BC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAI;AAAA,CACH,SAAUC,cAAa;AACtB,EAAAA,aAAY,aAAa,IAAI;AAC7B,EAAAA,aAAY,eAAe,IAAI;AAC/B,EAAAA,aAAY,aAAa,IAAI;AAC/B,GAAG,gBAAgB,cAAc,CAAC,EAAE;AACpC,IAAM,oBAAoB,IAAI,eAAe,mBAAmB;AAChE,IAAM,iBAAiB,IAAI,eAAe,gBAAgB;AAC1D,IAAM,kBAAkB,IAAI,eAAe,iBAAiB;AAC5D,IAAM,0BAA0B;AAChC,IAAM,sBAAsB;AAC5B,IAAM,wBAAwB;AAC9B,IAAM,0BAA0B;AAChC,IAAM,sCAAsC;AAC5C,IAAM,4BAA4B;AAClC,IAAM,mBAAmB,IAAI,eAAe,kBAAkB;AAC9D,IAAM,mBAAN,cAA+B,UAAS;AAAA,EACtC,cAAc;AACZ,UAAM,GAAG,SAAS;AAClB,SAAK,4CAA4C;AACjD,SAAK,yCAAyC;AAAA,EAChD;AACF;AACA,IAAM,mBAAN,MAAM,iBAAgB;AAAA,EACpB,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,QAAQ,OAAO;AACjB,SAAK,WAAW,kCACX,KAAK,yBACL;AAAA,EAEP;AAAA,EACA,IAAI,WAAW;AACb,WAAO,KAAK,QAAQ;AAAA,EACtB;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,SAAK,QAAQ,WAAW;AAAA,EAC1B;AAAA,EACA,YAAY,kBAAkB,YAAYC,UAAS,gBAAgB,UAAU,iBAAiB,MAAM,WAAW;AAC7G,SAAK,mBAAmB;AACxB,SAAK,aAAa;AAClB,SAAK,iBAAiB;AACtB,SAAK,WAAW;AAChB,SAAK,kBAAkB;AACvB,SAAK,OAAO;AACZ,SAAK,YAAY;AACjB,SAAK,yBAAyB;AAAA,MAC5B,UAAU,IAAI,UAAS;AAAA,IACzB;AACA,SAAK,wBAAwB;AAAA,MAC3B,YAAY,CAAC;AAAA,QACX,MAAM;AAAA,QACN,OAAO;AAAA,QACP,SAAS;AAAA,MACX,GAAG;AAAA,QACD,MAAM;AAAA,QACN,OAAO;AAAA,QACP,SAAS;AAAA,MACX,GAAG;AAAA,QACD,MAAM;AAAA,QACN,OAAO;AAAA,QACP,SAAS;AAAA,MACX,GAAG;AAAA,QACD,MAAM;AAAA,QACN,OAAO;AAAA,QACP,SAAS;AAAA,MACX,GAAG;AAAA,QACD,MAAM;AAAA,QACN,OAAO;AAAA,QACP,SAAS;AAAA,MACX,GAAG;AAAA,QACD,MAAM;AAAA,QACN,OAAO;AAAA,QACP,SAAS;AAAA,MACX,GAAG;AAAA,QACD,MAAM;AAAA,QACN,OAAO;AAAA,QACP,SAAS;AAAA,MACX,GAAG;AAAA,QACD,MAAM;AAAA,QACN,OAAO;AAAA,QACP,SAAS;AAAA,MACX,GAAG;AAAA,QACD,MAAM;AAAA,QACN,OAAO;AAAA,QACP,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AACA,SAAK,0BAA0B;AAAA,MAC7B,aAAa;AAAA,IACf;AACA,SAAK,4BAA4B;AAAA,MAC/B,iBAAiB;AAAA,IACnB;AACA,SAAK,wBAAwB;AAAA,MAC3B,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,SAAS;AAAA,MACT,eAAe;AAAA,MACf,kBAAkB;AAAA,IACpB;AACA,SAAK,yBAAyB;AAAA,MAC5B,WAAW;AAAA,MACX,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,cAAc;AAAA,MACd,SAAS;AAAA,MACT,gBAAgB;AAAA,IAClB;AACA,SAAK,WAAW,IAAI,QAAQ;AAC5B,SAAK,UAAU,KAAK,SAAS,aAAa;AAC1C,SAAK,UAAUA;AAAA,EACjB;AAAA,EACA,MAAM,UAAU,eAAe,KAAK,uBAAuB;AACzD,UAAM;AAAA,MACJ;AAAA,MACA,QAAAC;AAAA,MACA;AAAA,MACA,SAAAC;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,gBAAgB,kCACjB,KAAK,UACL,aAAa;AAElB,UAAM,WAAW,cAAc,YAAY,KAAK,YAAY,IAAI,UAAS;AACzE,QAAI,KAAK,YAAY;AACnB,WAAK,WAAW,KAAK,6BAA6B,QAAQ;AAAA,IAC5D;AACA,QAAIA,UAAS;AACX,WAAK,WAAW,KAAK,0BAA0B,QAAQ;AAAA,IACzD;AACA,UAAM,UAAU,KAAK,gBAAgB,QAAQ;AAC7C,UAAM,UAAU,aAAa,KAAK,WAAW,OAAO,IAAI;AACxD,UAAM,YAAY,QAAQ,KAAK,WAAW,OAAO,IAAI;AACrD,UAAMC,UAAS,KAAK,YAAY,WAAW,eAAeF,OAAM;AAChE,UAAM,YAAY,mBAAmBE,UAAS,KAAK,UAAU,SAAS,KAAK,iBAAiBA,OAAM;AAClG,WAAO,aAAa;AAAA,EACtB;AAAA,EACA,OAAO,SAASH,WAAU,KAAK,wBAAwB,kBAAkB;AACvE,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA,OAAAI;AAAA,MACA;AAAA,MACA,SAAAF;AAAA,MACA;AAAA,IACF,IAAIF;AACJ,QAAII,QAAO;AACT,WAAK,YAAY,SAAS,kCACrB,KAAK,wBACL,aACJ;AAAA,IACH;AACA,QAAIF,UAAS;AACX,WAAK,cAAc,SAAS,iDACvB,KAAK,0BACL,KAAK,iBACL,eACJ;AAAA,IACH;AACA,QAAI,WAAW;AACb,WAAK,gBAAgB,SAAS,kBAAkB,iDAC3C,KAAK,4BACL,KAAK,mBACL,iBACJ;AAAA,IACH;AACA,SAAK,UAAU,OAAO;AAAA,EACxB;AAAA,EACA,SAAS;AACP,SAAK,SAAS,KAAK;AAAA,EACrB;AAAA,EACA,UAAU,KAAK;AACb,QAAI,CAAC,KAAK,MAAM;AACd,YAAM,IAAI,MAAM,yBAAyB;AAAA,IAC3C;AACA,WAAO,KAAK,KAAK,IAAI,KAAK;AAAA,MACxB,cAAc;AAAA,IAChB,CAAC,EAAE,KAAK,IAAI,cAAY,KAAK,gBAAgB,KAAK,QAAQ,CAAC,CAAC;AAAA,EAC9D;AAAA,EACA,UAAU,SAAS;AACjB,QAAI,CAAC,kBAAkB,KAAK,QAAQ,GAAG;AACrC;AAAA,IACF;AACA,QAAI,OAAO,UAAU,eAAe,OAAO,MAAM,sBAAsB,aAAa;AAClF;AAAA,IACF;AACA,QAAI,CAAC,SAAS;AACZ,gBAAU;AAAA,IACZ;AACA,UAAM,qBAAqB,QAAQ,iBAAiB,oCAAoC;AACxF,UAAM,UAAU,QAAQ,KAAK,oBAAoB,OAAK,EAAE,UAAU,IAAI,eAAe,CAAC;AACtF,UAAM,kBAAkB,OAAO;AAAA,EACjC;AAAA,EACA,WAAWG,OAAM;AACf,QAAI,CAAC,kBAAkB,KAAK,QAAQ,GAAG;AACrC,aAAOA;AAAA,IACT;AACA,UAAM,WAAW,SAAS,cAAc,UAAU;AAClD,aAAS,YAAYA;AACrB,WAAO,SAAS;AAAA,EAClB;AAAA,EACA,6BAA6B,UAAU;AACrC,UAAM,mBAAmB;AACzB,QAAI,iBAAiB,8CAA8C,MAAM;AACvE,aAAO;AAAA,IACT;AACA,QAAI,KAAK,YAAY,SAAS,GAAG;AAC/B,aAAO,IAAI,GAAG,KAAK,UAAU;AAAA,IAC/B;AACA,qBAAiB,4CAA4C;AAC7D,WAAO;AAAA,EACT;AAAA,EACA,0BAA0B,UAAU;AAClC,UAAM,mBAAmB;AACzB,QAAI,iBAAiB,2CAA2C,MAAM;AACpE,aAAO;AAAA,IACT;AACA,UAAM,cAAc,SAAS;AAC7B,aAAS,OAAO,WAAS;AACvB,aAAO,MAAM,SAAS,YAAY,wBAAwB,MAAM,IAAI,WAAW,YAAY,KAAK;AAAA,IAClG;AACA,qBAAiB,yCAAyC;AAC1D,WAAO;AAAA,EACT;AAAA,EACA,gBAAgB,KAAK,UAAU;AAC7B,UAAM,mBAAmB,IAAI,YAAY,KAAK;AAC9C,UAAM,qBAAqB,mBAAmB,KAAK,IAAI,UAAU,mBAAmB,CAAC,IAAI;AACzF,UAAM,iBAAiB,mBAAmB,YAAY,GAAG;AACzD,UAAM,iBAAiB,iBAAiB,KAAK,mBAAmB,UAAU,iBAAiB,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC,IAAI;AAC9G,UAAM,eAAe,eAAe,YAAY,GAAG;AACnD,UAAM,YAAY,eAAe,KAAK,eAAe,UAAU,eAAe,CAAC,IAAI;AACnF,WAAO,CAAC,CAAC,aAAa,cAAc,OAAO,QAAQ,YAAY,OAAO,WAAW,UAAU;AAAA,EAC7F;AAAA,EACA,YAAYA,OAAM,eAAeJ,UAAS,OAAO;AAC/C,QAAI,cAAc,UAAU;AAG1B,YAAM,WAAW,mBACZ,cAAc;AAEnB,aAAO,SAAS;AAChB,aAAO,SAAS;AAGhB,aAAO,cAAc;AACrB,aAAO,IAAI;AAAA,QACT;AAAA,MACF,CAAC;AAAA,IACH;AACA,WAAOA,UAAS,OAAO,YAAYI,OAAM,aAAa,IAAI,OAAO,MAAMA,OAAM,aAAa;AAAA,EAC5F;AAAA,EACA,WAAWA,OAAM;AACf,QAAI,CAAC,kBAAkB,KAAK,QAAQ,GAAG;AACrC,aAAOA;AAAA,IACT;AACA,QAAI,OAAO,cAAc,eAAe,OAAO,UAAU,uBAAuB,aAAa;AAC3F,YAAM,IAAI,MAAM,uBAAuB;AAAA,IACzC;AACA,WAAO,UAAU,mBAAmBA,KAAI;AAAA,EAC1C;AAAA,EACA,YAAY,SAASL,UAAS;AAC5B,QAAI,CAAC,kBAAkB,KAAK,QAAQ,GAAG;AACrC;AAAA,IACF;AACA,QAAI,OAAO,UAAU,eAAe,OAAO,wBAAwB,aAAa;AAC9E,YAAM,IAAI,MAAM,mBAAmB;AAAA,IACrC;AACA,wBAAoB,SAASA,QAAO;AAAA,EACtC;AAAA,EACA,gBAAgB,SAAS,kBAAkBA,UAAS;AAClD,QAAI,CAAC,kBAAkB,KAAK,QAAQ,GAAG;AACrC;AAAA,IACF;AACA,QAAI,OAAO,gBAAgB,aAAa;AACtC,YAAM,IAAI,MAAM,uBAAuB;AAAA,IACzC;AACA,QAAI,CAAC,kBAAkB;AACrB,YAAM,IAAI,MAAM,mCAAmC;AAAA,IACrD;AACA,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAIA;AAEJ,UAAM,cAAc,QAAQ,iBAAiB,KAAK;AAClD,aAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK;AAC3C,YAAM,aAAa,YAAY,KAAK,CAAC;AAErC,YAAM,oBAAoB,SAAS,cAAc,KAAK;AACtD,wBAAkB,MAAM,WAAW;AACnC,iBAAW,WAAW,aAAa,mBAAmB,UAAU;AAChE,wBAAkB,YAAY,UAAU;AAExC,YAAM,wBAAwB,SAAS,cAAc,KAAK;AAC1D,4BAAsB,UAAU,IAAI,4BAA4B;AAChE,4BAAsB,MAAM,WAAW;AACvC,4BAAsB,MAAM,MAAM;AAClC,4BAAsB,MAAM,QAAQ;AACpC,4BAAsB,MAAM,SAAS;AACrC,wBAAkB,sBAAsB,aAAa,qBAAqB;AAE1E,wBAAkB,eAAe,MAAM,sBAAsB,UAAU,IAAI,OAAO;AAClF,wBAAkB,eAAe,MAAM,sBAAsB,UAAU,OAAO,OAAO;AAErF,UAAI;AAGJ,UAAI,iBAAiB;AACnB,cAAM,eAAe,iBAAiB,gBAAgB,eAAe;AACrE,0BAAkB,aAAa;AAC/B,qBAAa,kBAAkB,aAAa;AAAA,MAC9C,WAES,gBAAgB;AACvB,0BAAkB,iBAAiB,mBAAmB,cAAc;AAAA,MACtE,OAEK;AACH,cAAM,eAAe,iBAAiB,gBAAgB,wBAAwB;AAC9E,0BAAkB,aAAa;AAC/B,qBAAa,kBAAkB,aAAa;AAAA,MAC9C;AAEA,UAAI;AAEJ,sBAAgB,UAAU,QAAQ,UAAQ;AACxC,8BAAsB,YAAY,IAAI;AACtC,4BAAoB,IAAI,YAAY,MAAM;AAAA,UACxC,MAAM,MAAM,WAAW;AAAA,QACzB,CAAC;AAAA,MACH,CAAC;AAED,sBAAgB,UAAU,MAAM,kBAAkB,QAAQ,CAAC;AAAA,IAC7D;AAAA,EACF;AAAA,EACA,cAAc,SAASA,WAAU,KAAK,yBAAyB;AAC7D,QAAI,CAAC,kBAAkB,KAAK,QAAQ,GAAG;AACrC;AAAA,IACF;AACA,QAAI,OAAO,YAAY,eAAe,OAAO,QAAQ,eAAe,aAAa;AAC/E,YAAM,IAAI,MAAM,qBAAqB;AAAA,IACvC;AACA,UAAM,kBAAkB,QAAQ,iBAAiB,UAAU;AAC3D,QAAI,gBAAgB,WAAW,GAAG;AAChC;AAAA,IACF;AACA,YAAQ,WAAWA,QAAO;AAC1B,YAAQ,IAAI;AAAA,MACV,OAAO;AAAA,IACT,CAAC;AAAA,EACH;AAAA,EACA,gBAAgB,UAAU;AACxB,QAAI,CAAC,UAAU;AACb,aAAO;AAAA,IACT;AACA,QAAI;AACJ,WAAO,SAAS,MAAM,IAAI,EAAE,IAAI,UAAQ;AACtC,UAAI,iBAAiB;AACrB,UAAI,KAAK,SAAS,GAAG;AACnB,yBAAiB,MAAM,cAAc,IAAI,KAAK,OAAO,MAAM,IAAI,KAAK,IAAI,KAAK,OAAO,MAAM,GAAG,cAAc;AAAA,MAC7G;AACA,UAAI,MAAM,WAAW,GAAG;AACtB,sBAAc;AAAA,MAChB;AACA,aAAO,iBAAiB,KAAK,UAAU,cAAc,IAAI;AAAA,IAC3D,CAAC,EAAE,KAAK,IAAI;AAAA,EACd;AAYF;AAVI,iBAAK,OAAO,SAAS,wBAAwB,mBAAmB;AAC9D,SAAO,KAAK,qBAAqB,kBAAoB,SAAS,mBAAmB,CAAC,GAAM,SAAS,mBAAmB,CAAC,GAAM,SAAS,gBAAgB,CAAC,GAAM,SAAS,iBAAiB,CAAC,GAAM,SAAS,WAAW,GAAM,SAAS,gBAAgB,GAAM,SAAY,YAAY,CAAC,GAAM,SAAY,YAAY,CAAC;AAC/S;AAGA,iBAAK,QAA0B,mBAAmB;AAAA,EAChD,OAAO;AAAA,EACP,SAAS,iBAAgB;AAC3B,CAAC;AArWL,IAAM,kBAAN;AAAA,CAwWC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,cAAc;AAAA,IACvB,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,eAAe;AAAA,IACxB,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,IACpB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,qBAAN,MAAM,mBAAkB;AAAA,EACtB,IAAI,mBAAmB;AACrB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,iBAAiB,OAAO;AAC1B,SAAK,oBAAoB,KAAK,sBAAsB,KAAK;AAAA,EAC3D;AAAA,EACA,IAAI,SAAS;AACX,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,OAAO,OAAO;AAChB,SAAK,UAAU,KAAK,sBAAsB,KAAK;AAAA,EACjD;AAAA;AAAA,EAEA,IAAI,YAAY;AACd,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,UAAU,OAAO;AACnB,SAAK,aAAa,KAAK,sBAAsB,KAAK;AAAA,EACpD;AAAA;AAAA,EAEA,IAAI,QAAQ;AACV,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,MAAM,OAAO;AACf,SAAK,SAAS,KAAK,sBAAsB,KAAK;AAAA,EAChD;AAAA;AAAA,EAEA,IAAI,QAAQ;AACV,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,MAAM,OAAO;AACf,SAAK,SAAS,KAAK,sBAAsB,KAAK;AAAA,EAChD;AAAA;AAAA,EAEA,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,QAAQ,OAAO;AACjB,SAAK,WAAW,KAAK,sBAAsB,KAAK;AAAA,EAClD;AAAA;AAAA,EAEA,IAAI,gBAAgB;AAClB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,cAAc,OAAO;AACvB,SAAK,iBAAiB,KAAK,sBAAsB,KAAK;AAAA,EACxD;AAAA;AAAA,EAEA,IAAI,cAAc;AAChB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,YAAY,OAAO;AACrB,SAAK,eAAe,KAAK,sBAAsB,KAAK;AAAA,EACtD;AAAA;AAAA,EAEA,IAAI,cAAc;AAChB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,YAAY,OAAO;AACrB,SAAK,eAAe,KAAK,sBAAsB,KAAK;AAAA,EACtD;AAAA,EACA,YAAY,SAAS,iBAAiB,kBAAkB;AACtD,SAAK,UAAU;AACf,SAAK,kBAAkB;AACvB,SAAK,mBAAmB;AAExB,SAAK,QAAQ,IAAI,aAAa;AAC9B,SAAK,OAAO,IAAI,aAAa;AAC7B,SAAK,QAAQ,IAAI,aAAa;AAC9B,SAAK,aAAa;AAClB,SAAK,eAAe;AACpB,SAAK,oBAAoB;AACzB,SAAK,SAAS;AACd,SAAK,UAAU;AACf,SAAK,SAAS;AACd,SAAK,iBAAiB;AACtB,SAAK,eAAe;AACpB,SAAK,WAAW;AAChB,SAAK,aAAa,IAAI,QAAQ;AAAA,EAChC;AAAA,EACA,cAAc;AACZ,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,cAAc;AACZ,QAAI,KAAK,QAAQ,MAAM;AACrB,WAAK,WAAW;AAChB;AAAA,IACF;AACA,QAAI,KAAK,OAAO,MAAM;AACpB,WAAK,UAAU;AACf;AAAA,IACF;AAAA,EACF;AAAA,EACA,kBAAkB;AAChB,QAAI,CAAC,KAAK,QAAQ,CAAC,KAAK,KAAK;AAC3B,WAAK,mBAAmB;AAAA,IAC1B;AACA,SAAK,gBAAgB,QAAQ,KAAK,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,MAAM,KAAK,YAAY,CAAC;AAAA,EAClG;AAAA,EACA,cAAc;AACZ,SAAK,WAAW,KAAK;AACrB,SAAK,WAAW,SAAS;AAAA,EAC3B;AAAA,EACA,MAAM,OAAO,UAAU,aAAa,OAAO;AACzC,UAAM,gBAAgB;AAAA,MACpB;AAAA,MACA,QAAQ,KAAK;AAAA,MACb,OAAO,KAAK;AAAA,MACZ,SAAS,KAAK;AAAA,MACd,kBAAkB,KAAK;AAAA,IACzB;AACA,UAAM,gBAAgB;AAAA,MACpB,WAAW,KAAK;AAAA,MAChB,kBAAkB,KAAK,oBAAoB;AAAA,MAC3C,OAAO,KAAK;AAAA,MACZ,cAAc,KAAK;AAAA,MACnB,SAAS,KAAK;AAAA,MACd,gBAAgB,KAAK;AAAA,IACvB;AACA,UAAM,SAAS,MAAM,KAAK,gBAAgB,MAAM,UAAU,aAAa;AACvE,SAAK,QAAQ,cAAc,YAAY;AACvC,SAAK,cAAc;AACnB,SAAK,gBAAgB,OAAO,KAAK,QAAQ,eAAe,eAAe,KAAK,gBAAgB;AAC5F,SAAK,MAAM,KAAK;AAAA,EAClB;AAAA,EACA,sBAAsB,OAAO;AAC3B,WAAO,SAAS,QAAQ,GAAG,OAAO,KAAK,CAAC,OAAO;AAAA,EACjD;AAAA,EACA,sBAAsB;AACpB,QAAI,KAAK,4BAA4B,KAAK,yBAAyB;AACjE,aAAO;AAAA,QACL,iBAAiB,KAAK;AAAA,QACtB,gBAAgB,KAAK;AAAA,MACvB;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,aAAa;AACX,SAAK,OAAO,KAAK,IAAI;AAAA,EACvB;AAAA,EACA,YAAY;AACV,SAAK,gBAAgB,UAAU,KAAK,GAAG,EAAE,UAAU;AAAA,MACjD,MAAM,cAAY;AAChB,aAAK,OAAO,QAAQ,EAAE,KAAK,MAAM;AAC/B,eAAK,KAAK,KAAK,QAAQ;AAAA,QACzB,CAAC;AAAA,MACH;AAAA,MACA,OAAO,WAAS,KAAK,MAAM,KAAK,KAAK;AAAA,IACvC,CAAC;AAAA,EACH;AAAA,EACA,qBAAqB;AACnB,SAAK,OAAO,KAAK,QAAQ,cAAc,WAAW,IAAI;AAAA,EACxD;AAAA,EACA,gBAAgB;AACd,QAAI,KAAK,aAAa;AACpB,WAAK,eAAe,KAAK,QAAQ,eAAe,YAAY,WAAW;AACvE,WAAK,iBAAiB,KAAK,QAAQ,eAAe;AAAA,QAChD,kBAAkB,KAAK;AAAA,QACvB,UAAU,KAAK;AAAA,QACf,YAAY,KAAK;AAAA,QACjB,YAAY,KAAK;AAAA,QACjB,UAAU,KAAK;AAAA,MACjB,CAAC;AAAA,IACH;AACA,QAAI,KAAK,eAAe;AACtB,WAAK,iBAAiB,KAAK,QAAQ,eAAe;AAAA,QAChD,UAAU,KAAK;AAAA,QACf,gBAAgB,KAAK;AAAA,MACvB,CAAC;AAAA,IACH;AACA,QAAI,KAAK,aAAa;AACpB,WAAK,eAAe,KAAK,QAAQ,eAAe,YAAY,WAAW;AACvE,WAAK,iBAAiB,KAAK,QAAQ,eAAe;AAAA,QAChD,WAAW,KAAK;AAAA,MAClB,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,eAAe,SAAS,QAAQ;AAC9B,UAAM,cAAc,QAAQ,iBAAiB,KAAK;AAClD,aAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK;AAC3C,YAAM,UAAU,kBAAkB,QAAQ,SAAS,CAAC,MAAM;AAC1D,kBAAY,KAAK,CAAC,EAAE,UAAU,IAAI,GAAG,OAAO;AAAA,IAC9C;AAAA,EACF;AAAA,EACA,iBAAiB,SAASA,UAAS;AACjC,UAAM,cAAc,QAAQ,iBAAiB,KAAK;AAClD,aAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK;AAC3C,aAAO,KAAKA,QAAO,EAAE,QAAQ,YAAU;AACrC,cAAM,iBAAiBA,SAAQ,MAAM;AACrC,YAAI,gBAAgB;AAClB,gBAAM,gBAAgB,KAAK,WAAW,MAAM;AAC5C,sBAAY,KAAK,CAAC,EAAE,aAAa,eAAe,eAAe,SAAS,CAAC;AAAA,QAC3E;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,WAAW,OAAO;AAChB,UAAM,aAAa,MAAM,MAAM,UAAU;AACzC,QAAI,CAAC,YAAY;AACf,aAAO;AAAA,IACT;AACA,QAAI,MAAM,MAAM,SAAS;AACzB,aAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,IAAI,GAAG,KAAK;AACjD,YAAM,IAAI,QAAQ,IAAI,OAAO,WAAW,CAAC,CAAC,GAAG,MAAM,WAAW,CAAC,EAAE,YAAY,CAAC;AAAA,IAChF;AACA,QAAI,IAAI,MAAM,GAAG,CAAC,MAAM,KAAK;AAC3B,YAAM,IAAI,MAAM,CAAC;AAAA,IACnB;AACA,WAAO;AAAA,EACT;AAqDF;AAnDI,mBAAK,OAAO,SAAS,0BAA0B,mBAAmB;AAChE,SAAO,KAAK,qBAAqB,oBAAsB,kBAAqB,UAAU,GAAM,kBAAkB,eAAe,GAAM,kBAAqB,gBAAgB,CAAC;AAC3K;AAGA,mBAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,UAAU,GAAG,CAAC,IAAI,YAAY,EAAE,CAAC;AAAA,EAC9C,QAAQ;AAAA,IACN,MAAM;AAAA,IACN,KAAK;AAAA,IACL,kBAAkB;AAAA,IAClB,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,0BAA0B;AAAA,IAC1B,yBAAyB;AAAA,IACzB,OAAO;AAAA,IACP,OAAO;AAAA,IACP,cAAc;AAAA,IACd,SAAS;AAAA,IACT,gBAAgB;AAAA,IAChB,eAAe;AAAA,IACf,MAAM;AAAA,IACN,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,IACd,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,MAAM;AAAA,EACR;AAAA,EACA,SAAS;AAAA,IACP,OAAO;AAAA,IACP,MAAM;AAAA,IACN,OAAO;AAAA,EACT;AAAA,EACA,UAAU,CAAI,oBAAoB;AAAA,EAClC,oBAAoB;AAAA,EACpB,OAAO;AAAA,EACP,MAAM;AAAA,EACN,UAAU,SAAS,2BAA2B,IAAI,KAAK;AACrD,QAAI,KAAK,GAAG;AACV,MAAG,gBAAgB;AACnB,MAAG,aAAa,CAAC;AAAA,IACnB;AAAA,EACF;AAAA,EACA,eAAe;AACjB,CAAC;AArQL,IAAM,oBAAN;AAAA,CAwQC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA;AAAA,MAEL,UAAU;AAAA,MACV,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,KAAK,CAAC;AAAA,MACJ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,0BAA0B,CAAC;AAAA,MACzB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,yBAAyB,CAAC;AAAA,MACxB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,gBAAN,MAAM,cAAa;AAAA,EACjB,YAAY,cAAc,YAAY,iBAAiB,kBAAkB,MAAM;AAC7E,SAAK,eAAe;AACpB,SAAK,aAAa;AAClB,SAAK,kBAAkB;AACvB,SAAK,mBAAmB;AACxB,SAAK,OAAO;AAAA,EACd;AAAA,EACA,MAAM,UAAU,OAAOA,UAAS;AAC9B,QAAI,SAAS,MAAM;AACjB,aAAO;AAAA,IACT;AACA,QAAI,OAAO,UAAU,UAAU;AAC7B,cAAQ,MAAM,6DAA6D,OAAO,KAAK,GAAG;AAC1F,aAAO;AAAA,IACT;AACA,UAAM,WAAW,MAAM,KAAK,gBAAgB,MAAM,OAAOA,QAAO;AAChE,SAAK,KAAK,SAAS,KAAK,MAAM,CAAC,EAAE,UAAU,MAAM,KAAK,gBAAgB,OAAO,KAAK,WAAW,eAAeA,UAAS,KAAK,gBAAgB,CAAC;AAC3I,WAAO,KAAK,aAAa,wBAAwB,QAAQ;AAAA,EAC3D;AAaF;AAXI,cAAK,OAAO,SAAS,qBAAqB,mBAAmB;AAC3D,SAAO,KAAK,qBAAqB,eAAiB,kBAAqB,cAAc,EAAE,GAAM,kBAAqB,YAAY,EAAE,GAAM,kBAAkB,iBAAiB,EAAE,GAAM,kBAAqB,kBAAkB,EAAE,GAAM,kBAAqB,QAAQ,EAAE,CAAC;AAClQ;AAGA,cAAK,QAA0B,aAAa;AAAA,EAC1C,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AACR,CAAC;AA9BL,IAAM,eAAN;AAAA,CAiCC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,SAAS,gBAAgB,sBAAsB;AAC7C,SAAO,CAAC,iBAAiB,sBAAsB,UAAU,CAAC,GAAG,sBAAsB,oBAAoB,CAAC,GAAG,sBAAsB,iBAAiB,CAAC,GAAG,sBAAsB,kBAAkB,CAAC,GAAG,sBAAsB,oBAAoB,CAAC,GAAG;AAAA,IAC9O,SAAS;AAAA,IACT,UAAU,sBAAsB,YAAY,gBAAgB;AAAA,EAC9D,CAAC;AACH;AAGA,IAAM,qBAAqB,CAAC,0BAA0B,cAAc,mBAAmB,YAAY;AACnG,IAAM,kBAAN,MAAM,gBAAe;AAAA,EACnB,OAAO,QAAQ,sBAAsB;AACnC,WAAO;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC,gBAAgB,oBAAoB,CAAC;AAAA,IACnD;AAAA,EACF;AAAA,EACA,OAAO,WAAW;AAChB,WAAO;AAAA,MACL,UAAU;AAAA,IACZ;AAAA,EACF;AAgBF;AAdI,gBAAK,OAAO,SAAS,uBAAuB,mBAAmB;AAC7D,SAAO,KAAK,qBAAqB,iBAAgB;AACnD;AAGA,gBAAK,OAAyB,iBAAiB;AAAA,EAC7C,MAAM;AAAA,EACN,SAAS,CAAC,0BAA0B,cAAc,mBAAmB,YAAY;AAAA,EACjF,SAAS,CAAC,0BAA0B,cAAc,mBAAmB,YAAY;AACnF,CAAC;AAGD,gBAAK,OAAyB,iBAAiB,CAAC,CAAC;AAzBrD,IAAM,iBAAN;AAAA,CA4BC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS;AAAA,MACT,SAAS;AAAA,IACX,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["options", "PrismPlugin", "options", "inline", "mermaid", "marked", "katex", "html"]}