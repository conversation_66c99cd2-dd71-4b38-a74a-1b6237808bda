export interface Message {
  id?: string;
  type: 'human' | 'ai';
  content: string;
}

export interface ProcessedEvent {
  title: string;
  data: any;
}

export interface StreamConfig {
  messages: Message[];
  initial_search_query_count: number;
  max_research_loops: number;
  reasoning_model: string;
}

export interface ChatState {
  messages: Message[];
  isLoading: boolean;
  processedEventsTimeline: ProcessedEvent[];
  historicalActivities: Record<string, ProcessedEvent[]>;
}

export type EffortLevel = 'low' | 'medium' | 'high';
export type ModelType = 'gemini-2.0-flash' | 'gemini-2.5-flash-preview-04-17' | 'gemini-2.5-pro-preview-05-06';
