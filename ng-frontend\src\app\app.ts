import { Component, computed, effect } from '@angular/core';
import { WelcomeScreenComponent, ChatMessagesViewComponent } from './components';
import { LangGraphService } from './services/langgraph.service';
import { Message, EffortLevel, ModelType } from './models';

@Component({
  selector: 'app-root',
  imports: [WelcomeScreenComponent, ChatMessagesViewComponent],
  templateUrl: './app.html',
  styleUrl: './app.scss'
})
export class App {
  protected title = 'ng-frontend';

  constructor(private langGraphService: LangGraphService) {}

  // Expose service signals to template
  get messages() { return this.langGraphService.messages; }
  get isLoading() { return this.langGraphService.isLoading; }
  get processedEventsTimeline() { return this.langGraphService.processedEventsTimeline; }
  get historicalActivities() { return this.langGraphService.historicalActivities; }
  get hasMessages() { return this.langGraphService.hasMessages; }

  async handleSubmit(event: {
    inputValue: string;
    effort: EffortLevel;
    model: ModelType;
  }): Promise<void> {
    if (!event.inputValue.trim()) return;

    // Convert effort to search parameters
    let initial_search_query_count = 0;
    let max_research_loops = 0;

    switch (event.effort) {
      case "low":
        initial_search_query_count = 1;
        max_research_loops = 1;
        break;
      case "medium":
        initial_search_query_count = 3;
        max_research_loops = 3;
        break;
      case "high":
        initial_search_query_count = 5;
        max_research_loops = 10;
        break;
    }

    const newMessage: Message = {
      type: "human",
      content: event.inputValue,
      id: Date.now().toString(),
    };

    const newMessages: Message[] = [
      ...this.messages(),
      newMessage,
    ];

    await this.langGraphService.submitMessage({
      messages: newMessages,
      initial_search_query_count,
      max_research_loops,
      reasoning_model: event.model,
    });
  }

  handleCancel(): void {
    this.langGraphService.stop();
    window.location.reload();
  }
}
