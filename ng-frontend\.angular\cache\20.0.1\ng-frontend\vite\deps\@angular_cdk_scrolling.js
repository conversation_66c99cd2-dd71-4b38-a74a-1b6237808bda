import {
  CdkFixedSizeVirtualScroll,
  CdkScrollable,
  CdkScrollableModule,
  CdkVirtualForOf,
  CdkVirtualScrollViewport,
  CdkVirtualScrollable,
  CdkVirtualScrollableElement,
  CdkVirtualScrollableWindow,
  DEFAULT_RESIZE_TIME,
  DEFAULT_SCROLL_TIME,
  FixedSizeVirtualScrollStrategy,
  ScrollDispatcher,
  ScrollingModule,
  VIRTUAL_SCROLLABLE,
  VIRTUAL_SCROLL_STRATEGY,
  ViewportRuler,
  _fixedSizeVirtualScrollStrategyFactory
} from "./chunk-B7JS56WQ.js";
import "./chunk-WHHUYXI5.js";
import {
  Dir
} from "./chunk-FISNXLYS.js";
import "./chunk-NHOKLZZD.js";
import "./chunk-4VUWSNYE.js";
import "./chunk-3CS7CFSE.js";
import "./chunk-ZEMUWZO6.js";
export {
  CdkFixedSizeVirtualScroll,
  CdkScrollable,
  CdkScrollableModule,
  CdkVirtualForOf,
  CdkVirtualScrollViewport,
  CdkVirtualScrollable,
  CdkVirtualScrollableElement,
  CdkVirtualScrollableWindow,
  DEFAULT_RESIZE_TIME,
  DEFAULT_SCROLL_TIME,
  FixedSizeVirtualScrollStrategy,
  ScrollDispatcher,
  ScrollingModule,
  VIRTUAL_SCROLLABLE,
  VIRTUAL_SCROLL_STRATEGY,
  ViewportRuler,
  _fixedSizeVirtualScrollStrategyFactory,
  Dir as ɵɵDir
};
