import { Component, input, output, signal, ViewChild, ElementRef, AfterViewChecked } from '@angular/core';
import { LucideAngularModule, Loader2, <PERSON><PERSON>, <PERSON><PERSON><PERSON>heck } from 'lucide-angular';
import { InputFormComponent } from '../input-form/input-form.component';
import { ActivityTimelineComponent } from '../activity-timeline/activity-timeline.component';
import { Message, ProcessedEvent, EffortLevel, ModelType } from '../../models';

@Component({
  selector: 'app-chat-messages-view',
  standalone: true,
  imports: [
    LucideAngularModule,
    InputFormComponent,
    ActivityTimelineComponent
  ],
  template: `
    <div class="flex flex-col h-full">
      <div class="flex-grow overflow-y-auto" #scrollArea>
        <div class="p-4 md:p-6 space-y-2 max-w-4xl mx-auto pt-16">
          @for (message of messages(); track message.id || $index) {
            <div class="space-y-3">
              <div class="flex items-start gap-3"
                   [class.justify-end]="message.type === 'human'">
                @if (message.type === 'human') {
                  <!-- Human Message Bubble -->
                  <div class="text-white rounded-3xl break-words min-h-7 bg-neutral-700 max-w-[100%] sm:max-w-[90%] px-4 pt-3 rounded-br-lg">
                    <div [innerHTML]="message.content"></div>
                  </div>
                } @else {
                  <!-- AI Message Bubble -->
                  <div class="relative break-words flex flex-col">
                    @if (getActivityForMessage(message, $index).length > 0) {
                      <div class="mb-3 border-b border-neutral-700 pb-3 text-xs">
                        <app-activity-timeline
                          [processedEvents]="getActivityForMessage(message, $index)"
                          [isLoading]="isLiveActivityForMessage(message, $index)"
                        />
                      </div>
                    }
                    <div [innerHTML]="message.content"></div>
                    <button
                      class="cursor-pointer bg-neutral-700 border-neutral-600 text-neutral-300 self-end mt-2 px-3 py-2 rounded flex items-center gap-2"
                      (click)="handleCopy(message.content, message.id!)">
                      @if (copiedMessageId() === message.id) {
                        Copied
                        <lucide-angular [img]="CopyCheckIcon" class="h-4 w-4"></lucide-angular>
                      } @else {
                        Copy
                        <lucide-angular [img]="CopyIcon" class="h-4 w-4"></lucide-angular>
                      }
                    </button>
                  </div>
                }
              </div>
            </div>
          }

          @if (isLoading() && (messages().length === 0 || messages()[messages().length - 1].type === 'human')) {
            <div class="flex items-start gap-3 mt-3">
              <div class="relative group max-w-[85%] md:max-w-[80%] rounded-xl p-3 shadow-sm break-words bg-neutral-800 text-neutral-100 rounded-bl-none w-full min-h-[56px]">
                @if (liveActivityEvents().length > 0) {
                  <div class="text-xs">
                    <app-activity-timeline
                      [processedEvents]="liveActivityEvents()"
                      [isLoading]="true"
                    />
                  </div>
                } @else {
                  <div class="flex items-center justify-start h-full">
                    <lucide-angular [img]="Loader2Icon" class="h-5 w-5 animate-spin text-neutral-400 mr-2"></lucide-angular>
                    <span>Processing...</span>
                  </div>
                }
              </div>
            </div>
          }
        </div>
      </div>

      <app-input-form
        [isLoading]="isLoading()"
        [hasHistory]="messages().length > 0"
        (submit)="onSubmit($event)"
        (cancel)="onCancel()"
      />
    </div>
  `,
  styles: []
})
export class ChatMessagesViewComponent implements AfterViewChecked {
  // Input signals
  messages = input.required<Message[]>();
  isLoading = input.required<boolean>();
  liveActivityEvents = input.required<ProcessedEvent[]>();
  historicalActivities = input.required<Record<string, ProcessedEvent[]>>();

  // Output events
  submit = output<{
    inputValue: string;
    effort: EffortLevel;
    model: ModelType;
  }>();
  cancel = output<void>();

  // Component state
  copiedMessageId = signal<string | null>(null);

  // ViewChild for scrolling
  @ViewChild('scrollArea') scrollArea!: ElementRef;

  // Icons
  readonly Loader2Icon = Loader2;
  readonly CopyIcon = Copy;
  readonly CopyCheckIcon = CopyCheck;

  ngAfterViewChecked() {
    this.scrollToBottom();
  }

  private scrollToBottom(): void {
    try {
      if (this.scrollArea) {
        this.scrollArea.nativeElement.scrollTop = this.scrollArea.nativeElement.scrollHeight;
      }
    } catch (err) {
      console.error('Error scrolling to bottom:', err);
    }
  }

  getActivityForMessage(message: Message, index: number): ProcessedEvent[] {
    const isLastMessage = index === this.messages().length - 1;
    if (isLastMessage && this.isLoading()) {
      return this.liveActivityEvents();
    }
    return this.historicalActivities()[message.id!] || [];
  }

  isLiveActivityForMessage(message: Message, index: number): boolean {
    const isLastMessage = index === this.messages().length - 1;
    return isLastMessage && this.isLoading();
  }

  async handleCopy(text: string, messageId: string): Promise<void> {
    try {
      await navigator.clipboard.writeText(text);
      this.copiedMessageId.set(messageId);
      setTimeout(() => this.copiedMessageId.set(null), 2000);
    } catch (err) {
      console.error('Failed to copy text: ', err);
    }
  }

  onSubmit(event: { inputValue: string; effort: EffortLevel; model: ModelType }) {
    this.submit.emit(event);
  }

  onCancel() {
    this.cancel.emit();
  }
}
