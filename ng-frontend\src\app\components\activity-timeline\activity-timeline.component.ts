import { Component, input, signal, effect } from '@angular/core';
import { LucideAngularModule, Loader2, Activity, Info, Search, TextSearch, Brain, Pen, ChevronDown, ChevronUp } from 'lucide-angular';
import { ProcessedEvent } from '../../models';

@Component({
  selector: 'app-activity-timeline',
  standalone: true,
  imports: [LucideAngularModule],
  template: `
    <div class="border-none rounded-lg bg-neutral-700 max-h-96">
      <div class="p-4">
        <div class="flex items-center justify-between">
          <div
            class="flex items-center justify-start text-sm w-full cursor-pointer gap-2 text-neutral-100"
            (click)="toggleCollapse()">
            Research
            @if (isTimelineCollapsed()) {
              <lucide-angular [img]="ChevronDownIcon" class="h-4 w-4 mr-2"></lucide-angular>
            } @else {
              <lucide-angular [img]="ChevronUpIcon" class="h-4 w-4 mr-2"></lucide-angular>
            }
          </div>
        </div>
      </div>

      @if (!isTimelineCollapsed()) {
        <div class="max-h-96 overflow-y-auto">
          <div class="p-4 pt-0">
            @if (isLoading() && processedEvents().length === 0) {
              <div class="relative pl-8 pb-4">
                <div class="absolute left-3 top-3.5 h-full w-0.5 bg-neutral-800"></div>
                <div class="absolute left-0.5 top-2 h-5 w-5 rounded-full bg-neutral-800 flex items-center justify-center ring-4 ring-neutral-900">
                  <lucide-angular [img]="Loader2Icon" class="h-3 w-3 text-neutral-400 animate-spin"></lucide-angular>
                </div>
                <div>
                  <p class="text-sm text-neutral-300 font-medium">
                    Searching...
                  </p>
                </div>
              </div>
            }

            @if (processedEvents().length > 0) {
              <div class="space-y-0">
                @for (eventItem of processedEvents(); track $index) {
                  <div class="relative pl-8 pb-4">
                    @if ($index < processedEvents().length - 1 || (isLoading() && $index === processedEvents().length - 1)) {
                      <div class="absolute left-3 top-3.5 h-full w-0.5 bg-neutral-600"></div>
                    }
                    <div class="absolute left-0.5 top-2 h-6 w-6 rounded-full bg-neutral-600 flex items-center justify-center ring-4 ring-neutral-700">
                      <lucide-angular [img]="getEventIcon(eventItem.title, $index)" class="h-4 w-4 text-neutral-400"></lucide-angular>
                    </div>
                    <div>
                      <p class="text-sm text-neutral-200 font-medium mb-0.5">
                        {{ eventItem.title }}
                      </p>
                      <p class="text-xs text-neutral-300 leading-relaxed">
                        {{ formatEventData(eventItem.data) }}
                      </p>
                    </div>
                  </div>
                }

                @if (isLoading() && processedEvents().length > 0) {
                  <div class="relative pl-8 pb-4">
                    <div class="absolute left-0.5 top-2 h-5 w-5 rounded-full bg-neutral-600 flex items-center justify-center ring-4 ring-neutral-700">
                      <lucide-angular [img]="Loader2Icon" class="h-3 w-3 text-neutral-400 animate-spin"></lucide-angular>
                    </div>
                    <div>
                      <p class="text-sm text-neutral-300 font-medium">
                        Searching...
                      </p>
                    </div>
                  </div>
                }
              </div>
            } @else if (!isLoading()) {
              <div class="flex flex-col items-center justify-center h-full text-neutral-500 pt-10">
                <lucide-angular [img]="InfoIcon" class="h-6 w-6 mb-3"></lucide-angular>
                <p class="text-sm">No activity to display.</p>
                <p class="text-xs text-neutral-600 mt-1">
                  Timeline will update during processing.
                </p>
              </div>
            }
          </div>
        </div>
      }
    </div>
  `,
  styles: []
})
export class ActivityTimelineComponent {
  // Input signals
  processedEvents = input.required<ProcessedEvent[]>();
  isLoading = input.required<boolean>();

  // Component state
  isTimelineCollapsed = signal<boolean>(false);

  // Icons
  readonly Loader2Icon = Loader2;
  readonly ActivityIcon = Activity;
  readonly InfoIcon = Info;
  readonly SearchIcon = Search;
  readonly TextSearchIcon = TextSearch;
  readonly BrainIcon = Brain;
  readonly PenIcon = Pen;
  readonly ChevronDownIcon = ChevronDown;
  readonly ChevronUpIcon = ChevronUp;

  constructor() {
    // Effect to auto-collapse when loading finishes
    effect(() => {
      if (!this.isLoading() && this.processedEvents().length !== 0) {
        this.isTimelineCollapsed.set(true);
      }
    });
  }

  getEventIcon(title: string, index: number): any {
    if (index === 0 && this.isLoading() && this.processedEvents().length === 0) {
      return this.Loader2Icon;
    }
    if (title.toLowerCase().includes("generating")) {
      return this.TextSearchIcon;
    } else if (title.toLowerCase().includes("thinking")) {
      return this.Loader2Icon;
    } else if (title.toLowerCase().includes("reflection")) {
      return this.BrainIcon;
    } else if (title.toLowerCase().includes("research")) {
      return this.SearchIcon;
    } else if (title.toLowerCase().includes("finalizing")) {
      return this.PenIcon;
    }
    return this.ActivityIcon;
  }

  formatEventData(data: any): string {
    if (typeof data === "string") {
      return data;
    } else if (Array.isArray(data)) {
      return (data as string[]).join(", ");
    } else {
      return JSON.stringify(data);
    }
  }

  toggleCollapse(): void {
    this.isTimelineCollapsed.update(collapsed => !collapsed);
  }
}
