import { Component, input, output } from '@angular/core';
import { InputFormComponent } from '../input-form/input-form.component';
import { EffortLevel, ModelType } from '../../models';

@Component({
  selector: 'app-welcome-screen',
  standalone: true,
  imports: [InputFormComponent],
  template: `
    <div class="flex flex-col items-center justify-center text-center px-4 flex-1 w-full max-w-3xl mx-auto gap-4">
      <div>
        <h1 class="text-5xl md:text-6xl font-semibold text-neutral-100 mb-3">
          Welcome.
        </h1>
        <p class="text-xl md:text-2xl text-neutral-400">
          How can I help you today?
        </p>
      </div>
      <div class="w-full mt-4">
        <app-input-form
          [isLoading]="isLoading()"
          [hasHistory]="false"
          (submit)="onSubmit($event)"
          (cancel)="onCancel()"
        />
      </div>
      <p class="text-xs text-neutral-500">
        Powered by Google Gemini and <PERSON><PERSON><PERSON>n <PERSON>h.
      </p>
    </div>
  `,
  styles: []
})
export class WelcomeScreenComponent {
  // Input signals
  isLoading = input.required<boolean>();

  // Output events
  submit = output<{
    inputValue: string;
    effort: EffortLevel;
    model: ModelType;
  }>();
  cancel = output<void>();

  onSubmit(event: { inputValue: string; effort: EffortLevel; model: ModelType }) {
    this.submit.emit(event);
  }

  onCancel() {
    this.cancel.emit();
  }
}
