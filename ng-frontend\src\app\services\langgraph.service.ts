import { Injectable, signal, computed, effect } from '@angular/core';
import { BehaviorSubject, Subject } from 'rxjs';
import { Message, ProcessedEvent, StreamConfig } from '../models';

@Injectable({
  providedIn: 'root'
})
export class LangGraphService {
  // Signals for reactive state management
  private _messages = signal<Message[]>([]);
  private _isLoading = signal<boolean>(false);
  private _processedEventsTimeline = signal<ProcessedEvent[]>([]);
  private _historicalActivities = signal<Record<string, ProcessedEvent[]>>({});

  // Public readonly signals
  public readonly messages = this._messages.asReadonly();
  public readonly isLoading = this._isLoading.asReadonly();
  public readonly processedEventsTimeline = this._processedEventsTimeline.asReadonly();
  public readonly historicalActivities = this._historicalActivities.asReadonly();

  // Computed signals
  public readonly hasMessages = computed(() => this._messages().length > 0);
  public readonly lastMessage = computed(() => {
    const msgs = this._messages();
    return msgs.length > 0 ? msgs[msgs.length - 1] : null;
  });

  private apiUrl = this.getApiUrl();
  private currentStream: any = null;

  constructor() {
    // Effect to handle finalization of activities
    effect(() => {
      const isLoading = this._isLoading();
      const messages = this._messages();
      const timeline = this._processedEventsTimeline();
      
      if (!isLoading && messages.length > 0 && timeline.length > 0) {
        const lastMessage = messages[messages.length - 1];
        if (lastMessage && lastMessage.type === 'ai' && lastMessage.id) {
          this.finalizeActivity(lastMessage.id, timeline);
        }
      }
    });
  }

  private getApiUrl(): string {
    // Check if we're in development mode
    const isDev = !!(window as any)['ng'] && (window as any)['ng'].getDebugNode;
    return isDev ? "http://localhost:2024" : "http://localhost:8123";
  }

  public async submitMessage(config: StreamConfig): Promise<void> {
    this._messages.set(config.messages);
    this._isLoading.set(true);
    this._processedEventsTimeline.set([]);

    try {
      // Simulate LangGraph SDK streaming behavior
      await this.simulateStreaming(config);
    } catch (error) {
      console.error('Error in LangGraph streaming:', error);
      this._isLoading.set(false);
    }
  }

  private async simulateStreaming(config: StreamConfig): Promise<void> {
    // This is a simulation of the LangGraph SDK streaming
    // In a real implementation, you would integrate with the actual LangGraph SDK
    
    // Simulate processing events
    const events = [
      { generate_query: { query_list: ['search query 1', 'search query 2'] } },
      { web_research: { sources_gathered: [{ label: 'source 1' }, { label: 'source 2' }] } },
      { reflection: { is_sufficient: false, follow_up_queries: ['follow up 1'] } },
      { finalize_answer: {} }
    ];

    for (const event of events) {
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate delay
      this.processEvent(event);
    }

    // Simulate AI response
    const aiMessage: Message = {
      id: Date.now().toString(),
      type: 'ai',
      content: 'This is a simulated AI response with research results.'
    };

    this._messages.update(msgs => [...msgs, aiMessage]);
    this._isLoading.set(false);
  }

  private processEvent(event: any): void {
    let processedEvent: ProcessedEvent | null = null;

    if (event.generate_query) {
      processedEvent = {
        title: "Generating Search Queries",
        data: event.generate_query.query_list.join(", "),
      };
    } else if (event.web_research) {
      const sources = event.web_research.sources_gathered || [];
      const numSources = sources.length;
      const uniqueLabels = [
        ...new Set(sources.map((s: any) => s.label).filter(Boolean)),
      ];
      const exampleLabels = uniqueLabels.slice(0, 3).join(", ");
      processedEvent = {
        title: "Web Research",
        data: `Gathered ${numSources} sources. Related to: ${
          exampleLabels || "N/A"
        }.`,
      };
    } else if (event.reflection) {
      processedEvent = {
        title: "Reflection",
        data: event.reflection.is_sufficient
          ? "Search successful, generating final answer."
          : `Need more information, searching for ${event.reflection.follow_up_queries.join(
              ", "
            )}`,
      };
    } else if (event.finalize_answer) {
      processedEvent = {
        title: "Finalizing Answer",
        data: "Composing and presenting the final answer.",
      };
    }

    if (processedEvent) {
      this._processedEventsTimeline.update(events => [...events, processedEvent!]);
    }
  }

  private finalizeActivity(messageId: string, timeline: ProcessedEvent[]): void {
    this._historicalActivities.update(activities => ({
      ...activities,
      [messageId]: [...timeline]
    }));
  }

  public stop(): void {
    if (this.currentStream) {
      // Stop the current stream
      this.currentStream = null;
    }
    this._isLoading.set(false);
  }

  public reset(): void {
    this._messages.set([]);
    this._isLoading.set(false);
    this._processedEventsTimeline.set([]);
    this._historicalActivities.set({});
  }
}
