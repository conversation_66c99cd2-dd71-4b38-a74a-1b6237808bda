<h1 class="text-3xl font-bold underline">Hello world!</h1>
<div class="flex h-screen bg-neutral-800 text-neutral-100 font-sans antialiased">
  <main class="flex-1 flex flex-col overflow-hidden max-w-4xl mx-auto w-full">
    <div class="flex-1 overflow-y-auto" [class.flex]="!hasMessages()">
      @if (!hasMessages()) {
        <app-welcome-screen
          [isLoading]="isLoading()"
          (submit)="handleSubmit($event)"
          (cancel)="handleCancel()"
        />
      } @else {
        <app-chat-messages-view
          [messages]="messages()"
          [isLoading]="isLoading()"
          [liveActivityEvents]="processedEventsTimeline()"
          [historicalActivities]="historicalActivities()"
          (submit)="handleSubmit($event)"
          (cancel)="handleCancel()"
        />
      }
    </div>
  </main>
</div>
