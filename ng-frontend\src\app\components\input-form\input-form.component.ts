import { Component, input, output, signal } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { LucideAngularModule, SquarePen, Brain, Send, StopCircle, Zap, Cpu } from 'lucide-angular';
import { EffortLevel, ModelType } from '../../models';

@Component({
  selector: 'app-input-form',
  standalone: true,
  imports: [
    FormsModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    LucideAngularModule
  ],
  template: `
    <form (ngSubmit)="handleSubmit()" class="flex flex-col gap-2 p-3">
      <div class="flex flex-row items-center justify-between text-white rounded-3xl rounded-bl-sm 
                  break-words min-h-7 bg-neutral-700 px-4 pt-3"
           [class.rounded-br-sm]="hasHistory()">
        <textarea
          [(ngModel)]="inputValue"
          name="inputValue"
          (keydown)="handleKeyDown($event)"
          placeholder="Who won the Euro 2024 and scored the most goals?"
          class="w-full text-neutral-100 placeholder-neutral-500 resize-none border-0 
                 focus:outline-none focus:ring-0 outline-none bg-transparent
                 md:text-base min-h-[56px] max-h-[200px]"
          rows="1">
        </textarea>
        <div class="-mt-3">
          @if (isLoading()) {
            <button
              type="button"
              mat-icon-button
              class="text-red-500 hover:text-red-400 hover:bg-red-500/10 p-2 cursor-pointer rounded-full transition-all duration-200"
              (click)="handleCancel()">
              <lucide-angular [img]="StopCircleIcon" class="h-5 w-5"></lucide-angular>
            </button>
          } @else {
            <button
              type="submit"
              mat-button
              [disabled]="isSubmitDisabled()"
              [class]="isSubmitDisabled() 
                ? 'text-neutral-500' 
                : 'text-blue-500 hover:text-blue-400 hover:bg-blue-500/10'"
              class="p-2 cursor-pointer rounded-full transition-all duration-200 text-base">
              Search
              <lucide-angular [img]="SendIcon" class="h-5 w-5 ml-2"></lucide-angular>
            </button>
          }
        </div>
      </div>
      
      <div class="flex items-center justify-between">
        <div class="flex flex-row gap-2">
          <!-- Effort Selection -->
          <div class="flex flex-row gap-2 bg-neutral-700 border-neutral-600 text-neutral-300 
                      focus:ring-neutral-500 rounded-xl rounded-t-sm pl-2 max-w-[100%] sm:max-w-[90%]">
            <div class="flex flex-row items-center text-sm">
              <lucide-angular [img]="BrainIcon" class="h-4 w-4 mr-2"></lucide-angular>
              Effort
            </div>
            <mat-form-field appearance="fill" class="w-[120px] bg-transparent">
              <mat-select [(value)]="effort" class="bg-transparent border-none cursor-pointer">
                <mat-option value="low" class="hover:bg-neutral-600 focus:bg-neutral-600 cursor-pointer">
                  Low
                </mat-option>
                <mat-option value="medium" class="hover:bg-neutral-600 focus:bg-neutral-600 cursor-pointer">
                  Medium
                </mat-option>
                <mat-option value="high" class="hover:bg-neutral-600 focus:bg-neutral-600 cursor-pointer">
                  High
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>

          <!-- Model Selection -->
          <div class="flex flex-row gap-2 bg-neutral-700 border-neutral-600 text-neutral-300 
                      focus:ring-neutral-500 rounded-xl rounded-t-sm pl-2 max-w-[100%] sm:max-w-[90%]">
            <div class="flex flex-row items-center text-sm ml-2">
              <lucide-angular [img]="CpuIcon" class="h-4 w-4 mr-2"></lucide-angular>
              Model
            </div>
            <mat-form-field appearance="fill" class="w-[150px] bg-transparent">
              <mat-select [(value)]="model" class="bg-transparent border-none cursor-pointer">
                <mat-option value="gemini-2.0-flash" class="hover:bg-neutral-600 focus:bg-neutral-600 cursor-pointer">
                  <div class="flex items-center">
                    <lucide-angular [img]="ZapIcon" class="h-4 w-4 mr-2 text-yellow-400"></lucide-angular>
                    2.0 Flash
                  </div>
                </mat-option>
                <mat-option value="gemini-2.5-flash-preview-04-17" class="hover:bg-neutral-600 focus:bg-neutral-600 cursor-pointer">
                  <div class="flex items-center">
                    <lucide-angular [img]="ZapIcon" class="h-4 w-4 mr-2 text-orange-400"></lucide-angular>
                    2.5 Flash
                  </div>
                </mat-option>
                <mat-option value="gemini-2.5-pro-preview-05-06" class="hover:bg-neutral-600 focus:bg-neutral-600 cursor-pointer">
                  <div class="flex items-center">
                    <lucide-angular [img]="CpuIcon" class="h-4 w-4 mr-2 text-purple-400"></lucide-angular>
                    2.5 Pro
                  </div>
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>
        </div>

        @if (hasHistory()) {
          <button
            type="button"
            mat-button
            class="bg-neutral-700 border-neutral-600 text-neutral-300 cursor-pointer rounded-xl rounded-t-sm pl-2"
            (click)="handleNewSearch()">
            <lucide-angular [img]="SquarePenIcon" class="h-4 w-4 mr-2"></lucide-angular>
            New Search
          </button>
        }
      </div>
    </form>
  `,
  styles: [`
    ::ng-deep .mat-mdc-form-field {
      .mat-mdc-text-field-wrapper {
        background-color: transparent !important;
      }
      .mdc-notched-outline {
        border: none !important;
      }
    }
  `]
})
export class InputFormComponent {
  // Input signals
  isLoading = input.required<boolean>();
  hasHistory = input.required<boolean>();

  // Output events
  submit = output<{
    inputValue: string;
    effort: EffortLevel;
    model: ModelType;
  }>();
  cancel = output<void>();

  // Component state
  inputValue = signal('');
  effort = signal<EffortLevel>('medium');
  model = signal<ModelType>('gemini-2.5-flash-preview-04-17');

  // Icons
  readonly SquarePenIcon = SquarePen;
  readonly BrainIcon = Brain;
  readonly SendIcon = Send;
  readonly StopCircleIcon = StopCircle;
  readonly ZapIcon = Zap;
  readonly CpuIcon = Cpu;

  isSubmitDisabled() {
    return !this.inputValue().trim() || this.isLoading();
  }

  handleSubmit() {
    if (this.isSubmitDisabled()) return;
    
    this.submit.emit({
      inputValue: this.inputValue(),
      effort: this.effort(),
      model: this.model()
    });
    
    this.inputValue.set('');
  }

  handleKeyDown(event: KeyboardEvent) {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      this.handleSubmit();
    }
  }

  handleCancel() {
    this.cancel.emit();
  }

  handleNewSearch() {
    window.location.reload();
  }
}
